{"project": {"name": "fish-tank-surveillance", "description": "Video Surveillance Dashboard with RTSP to WebSocket/HLS conversion", "version": "1.0.0", "author": "<PERSON>"}, "packaging": {"include": {"directories": ["src", "public"], "files": ["package.json", "package-lock.json", "next.config.ts", "tsconfig.json", "tailwind.config.ts", "postcss.config.mjs", "eslint.config.mjs", "Dockerfile", "docker-compose.yml", "docker-compose.prod.yml", "docker-build.sh", ".dockerignore", "README.md", "FFMPEG_SETUP.md", ".giti<PERSON>re"]}, "exclude": {"directories": ["node_modules", ".next", ".git", "logs", "tmp", "temp", "package_temp"], "files": ["*.log", "*.tmp", ".env*", "*.pid", "*.seed", "*.pid.lock"], "patterns": ["public/streams/*", "*.bak", "*.backup", "*~", ".DS_Store", "Thumbs.db"]}, "optional": {"build": {"directory": ".next", "description": "Include pre-built Next.js files"}, "dependencies": {"directory": "node_modules", "description": "Include installed dependencies"}}}, "deployment": {"requirements": {"node": ">=18.0.0", "npm": ">=8.0.0", "system": {"linux": "FFmpeg required for video processing", "windows": "FFmpeg required for video processing", "docker": "Docker and Docker Compose for containerized deployment"}}, "ports": {"default": 3000, "description": "Next.js application server"}, "volumes": {"streams": "public/streams - Generated HLS stream files", "config": "public/config - Stream configuration", "logs": "logs - Application logs"}}, "scripts": {"unix": {"package": "./package.sh", "install": "./install.sh", "docker": "./docker-build.sh"}, "windows": {"package": "package.bat or package.ps1", "install": "install.bat or install.ps1", "docker": "docker-compose up"}}}