# FFmpeg Setup Guide for RTSP Streaming

This guide explains how to set up FFmpeg for RTSP to WebSocket/HLS conversion in your video surveillance dashboard.

## 🎯 Overview

The application now supports two streaming methods:
1. **WebSocket + JSMpeg** - Real-time, low-latency streaming
2. **HLS (HTTP Live Streaming)** - More compatible, works with native HTML5 video

## 📋 Prerequisites

### 1. Install FFmpeg

#### Windows:
```bash
# Using Chocolatey
choco install ffmpeg

# Or download from https://ffmpeg.org/download.html
# Add FFmpeg to your PATH environment variable
```

#### macOS:
```bash
# Using Homebrew
brew install ffmpeg
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install ffmpeg
```

### 2. Verify Installation
```bash
ffmpeg -version
```

## 🚀 How It Works

### WebSocket Streaming (JSMpeg)
1. FFmpeg converts RTSP → MPEG-TS format
2. Output is streamed to WebSocket clients
3. JSMpeg player decodes and displays in browser
4. **Pros**: Low latency, real-time
5. **Cons**: Limited browser support, requires WebSocket

### HLS Streaming
1. FFmpeg converts RTSP → HLS segments (.m3u8 + .ts files)
2. Files are served as static content
3. HTML5 video player loads HLS stream
4. **Pros**: Better browser compatibility, adaptive bitrate
5. **Cons**: Higher latency (2-6 seconds)

## 🔧 Configuration

### Stream Manager Settings

The application includes two stream managers:

#### WebSocket Stream Manager (`src/lib/streamManager.ts`)
- Starts WebSocket servers on ports 8080+
- Uses FFmpeg to convert RTSP to MPEG-TS
- Broadcasts to connected WebSocket clients

#### HLS Stream Manager (`src/lib/hlsStreamManager.ts`)
- Creates HLS playlists in `public/streams/`
- Uses FFmpeg to generate HLS segments
- Serves files through Next.js static serving

## 📝 Usage Examples

### Test RTSP URLs
For testing, you can use public RTSP streams:

```
# Example public streams (may not always be available)
rtsp://wowzaec2demo.streamlock.net/vod/mp4:BigBuckBunny_115k.mp4
rtsp://demo:<EMAIL>:5541/onvif-media/media.amp?profile=profile_1_h264
```

### Adding Streams
1. Enter RTSP URL: `rtsp://username:password@camera-ip:554/stream`
2. Choose stream type: WebSocket or HLS
3. Click "Add Camera"

## 🛠️ Troubleshooting

### Common Issues

#### 1. FFmpeg Not Found
```
Error: spawn ffmpeg ENOENT
```
**Solution**: Install FFmpeg and add to PATH

#### 2. RTSP Connection Failed
```
FFmpeg stderr: Connection refused
```
**Solutions**:
- Check RTSP URL format
- Verify camera credentials
- Test with VLC media player first
- Check network connectivity

#### 3. WebSocket Connection Failed
```
WebSocket connection failed
```
**Solutions**:
- Check if ports 8080+ are available
- Verify firewall settings
- Try HLS streaming instead

#### 4. HLS Playback Issues
```
Video element error
```
**Solutions**:
- Check if `public/streams/` directory exists
- Verify FFmpeg HLS output
- Try different browsers

### Debug Commands

Test RTSP stream with FFmpeg:
```bash
# Test basic connectivity
ffmpeg -i "rtsp://your-camera-url" -t 10 -f null -

# Test WebSocket output
ffmpeg -i "rtsp://your-camera-url" -f mpegts -codec:v mpeg1video -codec:a mp2 -b:v 1000k -b:a 128k -r 30 -bf 0 -muxdelay 0.001 -

# Test HLS output
ffmpeg -i "rtsp://your-camera-url" -c:v libx264 -c:a aac -preset ultrafast -tune zerolatency -f hls -hls_time 2 -hls_list_size 3 test.m3u8
```

## 🔒 Security Considerations

### Production Deployment
1. **Authentication**: Add user authentication
2. **HTTPS**: Use SSL certificates
3. **Rate Limiting**: Prevent abuse
4. **Resource Limits**: Limit concurrent streams
5. **Firewall**: Restrict access to streaming ports

### Environment Variables
Create `.env.local`:
```env
# Stream configuration
MAX_CONCURRENT_STREAMS=4
STREAM_BASE_PORT=8080
HLS_SEGMENT_TIME=2
HLS_LIST_SIZE=3

# Security
STREAM_AUTH_TOKEN=your-secret-token
```

## 📊 Performance Tips

### Optimize FFmpeg Settings

#### For Low Latency (WebSocket):
```bash
-preset ultrafast -tune zerolatency -fflags nobuffer -flags low_delay
```

#### For Quality (HLS):
```bash
-preset medium -crf 23 -maxrate 2000k -bufsize 4000k
```

#### For Multiple Streams:
- Limit concurrent streams
- Use hardware acceleration if available
- Monitor CPU/memory usage

### Hardware Acceleration
```bash
# NVIDIA GPU
-hwaccel cuda -c:v h264_nvenc

# Intel Quick Sync
-hwaccel qsv -c:v h264_qsv

# AMD GPU
-hwaccel vaapi -c:v h264_vaapi
```

## 🔄 Next Steps

1. **Test with your RTSP cameras**
2. **Choose streaming method** (WebSocket vs HLS)
3. **Configure security** for production
4. **Monitor performance** and optimize
5. **Add features** like recording, motion detection

## 📞 Support

If you encounter issues:
1. Check FFmpeg installation
2. Test RTSP URLs with VLC
3. Review browser console for errors
4. Check server logs for FFmpeg output
