#!/bin/bash

# Fish Tank Surveillance Dashboard - Packaging Script (Unix/Linux)
# Usage: ./package.sh [version] [--include-build]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="fish-tank-surveillance"
VERSION=${1:-"latest"}
INCLUDE_BUILD=${2:-""}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
ARCHIVE_NAME="${PROJECT_NAME}_${VERSION}_${TIMESTAMP}.tar.gz"
TEMP_DIR="./package_temp"

echo -e "${BLUE}🐟 Fish Tank Surveillance Dashboard - Packaging Script${NC}"
echo -e "${BLUE}====================================================${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${PURPLE}ℹ${NC} $1"
}

# Check if tar is available
if ! command -v tar &> /dev/null; then
    print_error "tar command not found. Please install tar."
    exit 1
fi

print_status "tar command found"

# Clean up any existing temp directory
if [ -d "$TEMP_DIR" ]; then
    print_info "Cleaning up existing temp directory..."
    rm -rf "$TEMP_DIR"
fi

# Create temp directory
mkdir -p "$TEMP_DIR"
print_status "Created temporary directory: $TEMP_DIR"

# Copy project files
print_info "Copying project files..."

# Essential files
cp -r src "$TEMP_DIR/"
cp -r public "$TEMP_DIR/"
cp package.json "$TEMP_DIR/"
cp package-lock.json "$TEMP_DIR/" 2>/dev/null || print_warning "package-lock.json not found"
cp next.config.ts "$TEMP_DIR/"
cp tsconfig.json "$TEMP_DIR/"
cp tailwind.config.ts "$TEMP_DIR/"
cp postcss.config.mjs "$TEMP_DIR/"
cp eslint.config.mjs "$TEMP_DIR/"

# Docker files
cp Dockerfile "$TEMP_DIR/"
cp docker-compose.yml "$TEMP_DIR/"
cp docker-compose.prod.yml "$TEMP_DIR/"
cp docker-build.sh "$TEMP_DIR/"
cp .dockerignore "$TEMP_DIR/"

# Documentation
cp README.md "$TEMP_DIR/" 2>/dev/null || print_warning "README.md not found"
cp FFMPEG_SETUP.md "$TEMP_DIR/" 2>/dev/null || print_warning "FFMPEG_SETUP.md not found"

# Git files (optional)
cp .gitignore "$TEMP_DIR/" 2>/dev/null || print_warning ".gitignore not found"

print_status "Essential files copied"

# Include build files if requested
if [ "$INCLUDE_BUILD" = "--include-build" ]; then
    if [ -d ".next" ]; then
        print_info "Including build files..."
        cp -r .next "$TEMP_DIR/"
        print_status "Build files included"
    else
        print_warning "No build files found. Run 'npm run build' first."
    fi
fi

# Create version info file
cat > "$TEMP_DIR/VERSION_INFO.txt" << EOF
Fish Tank Surveillance Dashboard
================================

Version: $VERSION
Build Date: $(date)
Build Host: $(hostname)
Build User: $(whoami)
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo "Not available")
Git Branch: $(git branch --show-current 2>/dev/null || echo "Not available")

Package Contents:
- Source code (src/)
- Public assets (public/)
- Configuration files
- Docker setup
- Documentation

Installation:
1. Extract the archive
2. Run: npm install
3. Configure streams in public/config/streams.json
4. Build: npm run build
5. Start: npm start

Docker Deployment:
1. Extract the archive
2. Run: ./docker-build.sh
3. Access: http://localhost:3000
EOF

print_status "Version info created"

# Create installation script
cat > "$TEMP_DIR/install.sh" << 'EOF'
#!/bin/bash

echo "🐟 Fish Tank Surveillance Dashboard - Installation"
echo "================================================"

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js not found. Please install Node.js 18+ first."
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ required. Current: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) found"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Create directories
mkdir -p public/streams logs

echo "🔧 Setting up configuration..."
if [ ! -f "public/config/streams.json" ]; then
    echo "⚠️  Please configure your streams in public/config/streams.json"
fi

echo "🏗️  Building application..."
npm run build

echo "✅ Installation complete!"
echo ""
echo "Next steps:"
echo "1. Configure streams: edit public/config/streams.json"
echo "2. Start application: npm start"
echo "3. Access dashboard: http://localhost:3000"
EOF

chmod +x "$TEMP_DIR/install.sh"
print_status "Installation script created"

# Create the tar archive
print_info "Creating tar archive: $ARCHIVE_NAME"
tar -czf "$ARCHIVE_NAME" -C "$TEMP_DIR" .

# Get archive size
ARCHIVE_SIZE=$(du -h "$ARCHIVE_NAME" | cut -f1)
print_status "Archive created: $ARCHIVE_NAME ($ARCHIVE_SIZE)"

# Clean up temp directory
rm -rf "$TEMP_DIR"
print_status "Cleaned up temporary files"

# Show archive contents
print_info "Archive contents:"
tar -tzf "$ARCHIVE_NAME" | head -20
if [ $(tar -tzf "$ARCHIVE_NAME" | wc -l) -gt 20 ]; then
    echo "... and $(( $(tar -tzf "$ARCHIVE_NAME" | wc -l) - 20 )) more files"
fi

echo ""
echo -e "${GREEN}🎉 Package created successfully!${NC}"
echo -e "${BLUE}Archive: ${NC}$ARCHIVE_NAME"
echo -e "${BLUE}Size: ${NC}$ARCHIVE_SIZE"
echo ""
echo -e "${YELLOW}Usage:${NC}"
echo "  Extract: tar -xzf $ARCHIVE_NAME"
echo "  Install: cd extracted_folder && ./install.sh"
echo "  Docker:  cd extracted_folder && ./docker-build.sh"
