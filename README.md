# Fish Tank - Video Surveillance Dashboard

A Next.js application for displaying multiple RTSP streams in a 4-grid layout, similar to iSpy or ZoneMinder.

## Features

- **4-Grid Layout**: Display up to 4 video streams simultaneously
- **RTSP Stream Support**: Add RTSP camera streams (requires FFmpeg conversion)
- **Stream Management**: Add/remove camera streams dynamically
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Video**: Uses JSMpeg for browser video playback

## Getting Started

1. Install dependencies:
```bash
npm install
```

2. Run the development server:
```bash
npm run dev
```

3. Open [http://localhost:3000](http://localhost:3000) with your browser.

## Usage

1. **Add Camera Stream**: Enter an RTSP URL (e.g., `rtsp://username:password@camera-ip:554/stream`) and optional camera name
2. **View Streams**: Added streams will appear in a 2x2 grid layout
3. **Remove Streams**: Click "Remove" next to any stream in the management section

## ✅ FFmpeg Integration Complete!

### RTSP Stream Conversion
The application now includes **full FFmpeg integration** with two streaming methods:

#### 🔴 WebSocket Streaming (JSMpeg)
- **Real-time, low-latency** streaming
- FFmpeg converts RTSP → MPEG-TS → WebSocket
- Uses JSMpeg player for browser playback
- Best for: Live monitoring, security applications

#### 📺 HLS Streaming (HTML5)
- **Better browser compatibility**
- FFmpeg converts RTSP → HLS segments
- Uses native HTML5 video player
- Best for: General viewing, mobile devices

### Setup Requirements
1. **Install FFmpeg** (see `FFMPEG_SETUP.md`)
2. **Choose streaming method** when adding cameras
3. **Configure your RTSP URLs**

### Stream Management
- ✅ Dynamic start/stop streams
- ✅ Multiple concurrent streams
- ✅ Process management and cleanup
- ✅ Real-time status monitoring
- ✅ Error handling and recovery

## Technology Stack

- **Next.js 15** - React framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **JSMpeg** - Browser video player
- **WebSocket** - Real-time streaming (requires server setup)

## Development

The project structure:
- `src/components/VideoGrid.tsx` - Grid layout component
- `src/components/VideoStream.tsx` - Individual stream component
- `src/app/page.tsx` - Main dashboard page
- `src/app/api/stream/route.ts` - Stream API endpoint (placeholder)

## 🚀 Quick Start

1. **Install FFmpeg**:
   ```bash
   # Windows (Chocolatey)
   choco install ffmpeg

   # macOS (Homebrew)
   brew install ffmpeg

   # Linux (Ubuntu)
   sudo apt install ffmpeg
   ```

2. **Start the application**:
   ```bash
   npm run dev
   ```

3. **Add RTSP streams**:
   - Enter your camera's RTSP URL
   - Choose WebSocket or HLS streaming
   - Click "Add Camera"

## 📁 Project Structure

- `src/components/VideoGrid.tsx` - Grid layout component
- `src/components/VideoStream.tsx` - WebSocket stream component
- `src/components/HLSVideoStream.tsx` - HLS stream component
- `src/app/page.tsx` - Main dashboard page
- `src/app/api/stream/route.ts` - WebSocket stream API
- `src/app/api/hls-stream/route.ts` - HLS stream API
- `src/lib/streamManager.ts` - WebSocket stream management
- `src/lib/hlsStreamManager.ts` - HLS stream management

## 🔧 Production Deployment

For production use:
1. **Security**: Add authentication and HTTPS
2. **Performance**: Configure hardware acceleration
3. **Monitoring**: Set up logging and alerts
4. **Scaling**: Use load balancers for multiple streams
5. **Storage**: Add recording and playback features

See `FFMPEG_SETUP.md` for detailed configuration guide.
