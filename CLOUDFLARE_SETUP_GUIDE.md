# Cloudflare Tunnel Setup for Surveillance Streaming

## Why Cloudflare Tunnel Instead of Cloudflare Stream?

### Cost Comparison:
- **Cloudflare Stream**: $2,000+/month for 24/7 surveillance
- **Cloudflare Tunnel**: $0 (free tier) + your server costs
- **Savings**: 95%+ cost reduction

### Benefits of Cloudflare Tunnel:
✅ **Free HTTPS** with automatic SSL certificates  
✅ **Global CDN** acceleration  
✅ **DDoS protection** included  
✅ **No bandwidth charges** for reasonable usage  
✅ **Keep your optimized streaming** setup  
✅ **Secure access** without exposing your server IP  

## Setup Instructions

### 1. Install Cloudflare Tunnel

```bash
# Download cloudflared
curl -L --output cloudflared.deb https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
sudo dpkg -i cloudflared.deb

# Or for other systems, see: https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation/
```

### 2. Authenticate with Cloudflare

```bash
cloudflared tunnel login
```

This opens a browser to authenticate with your Cloudflare account.

### 3. Create a Tunnel

```bash
# Create tunnel
cloudflared tunnel create surveillance-stream

# Note the tunnel ID that's generated
```

### 4. Configure DNS

```bash
# Add DNS record pointing to your tunnel
cloudflared tunnel route dns surveillance-stream surveillance.yourdomain.com
```

### 5. Create Configuration File

Create `/etc/cloudflared/config.yml`:

```yaml
tunnel: surveillance-stream
credentials-file: /root/.cloudflared/[TUNNEL-ID].json

ingress:
  # Main web interface
  - hostname: surveillance.yourdomain.com
    service: http://localhost:3000
    
  # WebSocket streams (if needed separately)
  - hostname: ws.surveillance.yourdomain.com
    service: http://localhost:8080
    
  # HLS streams
  - hostname: hls.surveillance.yourdomain.com
    service: http://localhost:3000
    originRequest:
      httpHostHeader: localhost:3000
      
  # Catch-all rule (required)
  - service: http_status:404
```

### 6. Run the Tunnel

```bash
# Test the tunnel
cloudflared tunnel run surveillance-stream

# Install as a service (Linux)
sudo cloudflared service install
sudo systemctl start cloudflared
sudo systemctl enable cloudflared
```

## Advanced Configuration

### For High-Traffic Surveillance

If you expect high traffic, consider these optimizations:

```yaml
tunnel: surveillance-stream
credentials-file: /root/.cloudflared/[TUNNEL-ID].json

# Performance optimizations
originRequest:
  connectTimeout: 30s
  tlsTimeout: 10s
  tcpKeepAlive: 30s
  keepAliveConnections: 100
  keepAliveTimeout: 90s

ingress:
  - hostname: surveillance.yourdomain.com
    service: http://localhost:3000
    originRequest:
      # Disable buffering for live streams
      disableChunkedEncoding: true
      # Increase timeouts for long-running streams
      noTLSVerify: false
      
  - service: http_status:404
```

### Environment Variables for Your App

Update your `.env.local`:

```env
# Cloudflare Tunnel URLs
NEXT_PUBLIC_BASE_URL=https://surveillance.yourdomain.com
WS_HOST=ws.surveillance.yourdomain.com
WS_PROTOCOL=wss
WS_PORT=443

# Keep your optimized settings
STREAM_QUALITY=optimized
ENABLE_WEBRTC=true
```

## Security Configuration

### 1. Access Control (Optional)

Add Cloudflare Access for additional security:

```yaml
ingress:
  - hostname: surveillance.yourdomain.com
    service: http://localhost:3000
    originRequest:
      # Add Cloudflare Access headers
      httpHostHeader: surveillance.yourdomain.com
```

### 2. Rate Limiting

Configure rate limiting in Cloudflare dashboard:
- **Surveillance Dashboard**: 100 requests/minute per IP
- **Stream Endpoints**: 1000 requests/minute per IP
- **WebSocket Connections**: 50 connections/minute per IP

## Monitoring & Analytics

### 1. Cloudflare Analytics

Monitor your tunnel performance:
- **Bandwidth Usage**: Track data transfer
- **Request Patterns**: Identify peak usage times
- **Error Rates**: Monitor stream reliability
- **Geographic Distribution**: See where viewers are located

### 2. Custom Monitoring

Add monitoring endpoints to your app:

```typescript
// pages/api/health.ts
export default function handler(req, res) {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    streams: streamManager.getStreamStats(),
    connections: connectionManager.getHealthStats()
  });
}
```

## Cost Optimization

### Free Tier Limits:
- **Bandwidth**: Generous allowance for most surveillance setups
- **Requests**: Unlimited for reasonable usage
- **Tunnels**: Up to 50 tunnels per account

### If You Exceed Free Tier:
- **Cloudflare Pro**: $20/month for enhanced features
- **Still 95% cheaper** than Cloudflare Stream
- **Better performance** with your optimized setup

## Deployment Checklist

- [ ] Install cloudflared on your server
- [ ] Create and configure tunnel
- [ ] Set up DNS records
- [ ] Update environment variables
- [ ] Test stream performance
- [ ] Configure monitoring
- [ ] Set up automatic tunnel restart
- [ ] Document access URLs for users

## Troubleshooting

### Common Issues:

1. **Tunnel Not Starting**
   ```bash
   # Check logs
   journalctl -u cloudflared -f
   ```

2. **WebSocket Connection Issues**
   ```yaml
   # Add to config.yml
   originRequest:
     httpHostHeader: localhost:8080
   ```

3. **Stream Buffering**
   ```yaml
   # Disable buffering
   originRequest:
     disableChunkedEncoding: true
   ```

## Next Steps

1. **Set up the tunnel** following this guide
2. **Test with optimized FFmpeg settings** from the main optimization guide
3. **Monitor performance** and adjust as needed
4. **Consider WebRTC implementation** for ultra-low latency
5. **Scale horizontally** if needed with multiple servers

This setup gives you enterprise-grade streaming infrastructure at a fraction of the cost of cloud streaming services!
