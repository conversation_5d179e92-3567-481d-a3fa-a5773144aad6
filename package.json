{"name": "fish_tank", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "node server.js", "start:next": "next start", "lint": "next lint"}, "dependencies": {"@cycjimmy/jsmpeg-player": "^6.1.2", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "next": "15.3.4", "node-ffmpeg-stream": "^1.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "uuid": "^11.1.0", "ws": "^8.18.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "ignore-loader": "^0.1.2", "tailwindcss": "^4", "typescript": "^5"}}