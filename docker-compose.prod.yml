services:
  incubase-surveillance:
    # Use pre-built image in production
    image: incubasestudio.com/incubase-surv:v0.0.1
    # Add port mapping
    ports:
      - "8349:3000"
      - "6080:8080"
    restart: unless-stopped
    # Production environment variables (override base configuration)
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - PORT=3000
      - HOSTNAME=0.0.0.0
      # WebSocket configuration for production (fully configurable)
      - WS_HOST=${WS_HOST:-localhost}
      - WS_PORT=${WS_PORT:-443}
      - WS_PROTOCOL=${WS_PROTOCOL:-wss}
      # SSL/TLS configuration for production
      - SSL_KEY_PATH=${SSL_KEY_PATH:-/app/ssl/privkey.pem}
      - SSL_CERT_PATH=${SSL_CERT_PATH:-/app/ssl/fullchain.pem}
      # Production-specific overrides
      - LOG_LEVEL=${LOG_LEVEL:-warn}
      - MAX_STREAMS=${MAX_STREAMS:-20}
      - DEFAULT_VIDEO_BITRATE=${DEFAULT_VIDEO_BITRATE:-1500k}
      - DEFAULT_AUDIO_BITRATE=${DEFAULT_AUDIO_BITRATE:-192k}
      - DEFAULT_FRAME_RATE=${DEFAULT_FRAME_RATE:-30}
    # Production logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # Additional security for production
    security_opt:
      - no-new-privileges:true
      - apparmor:unconfined
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    # Read-only root filesystem for security
    read_only: true
    volumes:
      # Persist stream configuration (remove :ro to make writable)
      - /volume1/docker/incubase-surv/public/config/streams.json:/app/public/config/streams.json
