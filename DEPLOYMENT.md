# Fish Tank Surveillance Dashboard - Deployment Guide

This guide covers deployment scenarios and WebSocket configuration for different environments.

## 🚀 Quick Fix for WebSocket Connection Issues

If you're seeing `WebSocket connection to 'ws://localhost:8080/' failed`, this means the WebSocket host needs to be configured for your deployment environment.

### Local Development
```bash
# No configuration needed - uses localhost by default
npm run dev
```

### Docker Local Deployment
```bash
# Uses localhost - works for local Docker
docker-compose up -d
```

### Docker Remote Server Deployment
```bash
# Set the WebSocket host to your server's IP or domain
export WS_HOST=your-server-ip-or-domain.com
docker-compose up -d
```

### Production Server Deployment
```bash
# For production, set WS_HOST to your public domain/IP
export WS_HOST=surveillance.yourdomain.com
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 🔧 Environment Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```bash
# Copy example configuration
cp .env.example .env

# Edit configuration
nano .env
```

Key variables for WebSocket configuration:
- `WS_HOST`: The hostname/IP where WebSocket server is accessible
- `WS_PORT`: The port for WebSocket connections (default: 8080)

### Common Deployment Scenarios

#### 1. Local Development
```env
WS_HOST=localhost
WS_PORT=8080
```

#### 2. Local Docker
```env
WS_HOST=localhost
WS_PORT=8080
```

#### 3. Remote Server (IP Address)
```env
WS_HOST=*************
WS_PORT=8080
```

#### 4. Remote Server (Domain)
```env
WS_HOST=surveillance.company.com
WS_PORT=8080
```

#### 5. Behind Reverse Proxy (Nginx/Apache)
```env
WS_HOST=surveillance.company.com
WS_PORT=80  # or 443 for HTTPS
```

## 🐳 Docker Deployment

### Development
```bash
# Build and start
docker-compose up -d

# View logs
docker-compose logs -f

# Stop
docker-compose down
```

### Production
```bash
# Set production WebSocket host
export WS_HOST=your-production-domain.com

# Start production stack
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# View logs
docker-compose logs -f fish-tank-surveillance
```

### Custom Configuration
```bash
# Create custom environment file
cat > .env.prod << EOF
WS_HOST=surveillance.mycompany.com
WS_PORT=8080
NODE_ENV=production
LOG_LEVEL=warn
MAX_STREAMS=20
EOF

# Use custom environment
docker-compose --env-file .env.prod up -d
```

## 🌐 Reverse Proxy Configuration

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name surveillance.yourdomain.com;

    # Main application
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket proxy
    location /ws {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Apache Configuration
```apache
<VirtualHost *:80>
    ServerName surveillance.yourdomain.com
    
    # Main application
    ProxyPass / http://localhost:3000/
    ProxyPassReverse / http://localhost:3000/
    
    # WebSocket proxy
    ProxyPass /ws ws://localhost:8080/ws
    ProxyPassReverse /ws ws://localhost:8080/ws
    
    # Headers
    ProxyPreserveHost On
    ProxyAddHeaders On
</VirtualHost>
```

## 🔒 Security Considerations

### Firewall Configuration
```bash
# Allow HTTP/HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow WebSocket port (if not behind reverse proxy)
sudo ufw allow 8080

# Allow SSH (if needed)
sudo ufw allow 22

# Enable firewall
sudo ufw enable
```

### Docker Security
```yaml
# In docker-compose.prod.yml
security_opt:
  - no-new-privileges:true
  - apparmor:unconfined
cap_drop:
  - ALL
cap_add:
  - CHOWN
  - SETGID
  - SETUID
read_only: true
```

## 🔍 Troubleshooting

### WebSocket Connection Issues

1. **Check WebSocket URL**:
   ```bash
   # In browser console
   console.log('WebSocket URL:', wsUrl);
   ```

2. **Test WebSocket connectivity**:
   ```bash
   # Test from command line
   wscat -c ws://your-host:8080/ws?streamId=test
   ```

3. **Check Docker logs**:
   ```bash
   docker-compose logs fish-tank-surveillance
   ```

4. **Verify port accessibility**:
   ```bash
   # Test if port is open
   telnet your-host 8080
   ```

### Common Issues and Solutions

#### Issue: "WebSocket connection failed"
**Solution**: Set correct `WS_HOST` environment variable

#### Issue: "Connection refused"
**Solution**: Ensure port 8080 is exposed and accessible

#### Issue: "Stream not found"
**Solution**: Check if stream is properly started via API

#### Issue: "FFmpeg not found"
**Solution**: Ensure FFmpeg is installed in Docker container

### Health Checks
```bash
# Check application health
curl http://localhost:3000/api/config

# Check WebSocket server
curl -I http://localhost:8080

# Check Docker container health
docker-compose ps
```

## 📊 Monitoring

### Log Monitoring
```bash
# Follow application logs
docker-compose logs -f fish-tank-surveillance

# Check specific service logs
docker logs fish-tank-surveillance

# Monitor WebSocket connections
docker exec fish-tank-surveillance netstat -an | grep 8080
```

### Performance Monitoring
```bash
# Check container resource usage
docker stats fish-tank-surveillance

# Monitor stream count
curl http://localhost:3000/api/streams
```

## 🚀 Production Checklist

- [ ] Set correct `WS_HOST` for your domain/IP
- [ ] Configure reverse proxy (if using)
- [ ] Set up SSL/TLS certificates
- [ ] Configure firewall rules
- [ ] Set up log rotation
- [ ] Configure monitoring
- [ ] Test WebSocket connectivity
- [ ] Test stream functionality
- [ ] Set up backup for configuration
- [ ] Document deployment for team
