@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Fullscreen styles */
.fullscreen-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: black;
}

/* Hide scrollbars in fullscreen mode */
.fullscreen-container::-webkit-scrollbar {
  display: none;
}

.fullscreen-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Smooth transitions for grid layout changes */
.video-grid-transition {
  transition: all 0.3s ease-in-out;
}

/* Ensure video elements fill their containers properly */
.video-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-container video,
.video-container canvas {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Camera name overlay styles - Always on top with highest z-index */
.camera-name-overlay {
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  z-index: 9999 !important;
  pointer-events: none; /* Allow clicks to pass through to video */
}

/* Status indicator animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.status-indicator {
  animation: pulse 2s infinite;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.status-indicator.connected {
  animation: none;
  box-shadow: 0 0 8px rgba(34, 197, 94, 0.6);
}

/* Text shadow for better readability */
.camera-label {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9);
  letter-spacing: 0.025em;
}

/* Ensure labels are always on top with proper stacking context */
.video-container {
  position: relative;
  z-index: 1;
}

/* Force camera labels to always be on top */
.camera-name-overlay {
  position: absolute !important;
  z-index: 9999 !important;
}

/* Logo styling - Always visible */
.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-container img {
  max-height: 3rem;
  width: auto;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  min-width: 48px; /* Reserve space for logo */
  background: transparent;
}

/* Responsive logo sizing */
@media (max-width: 768px) {
  .logo-container img {
    max-height: 2rem;
  }

  .logo-container h1 {
    font-size: 1.25rem;
  }

  .logo-container p {
    font-size: 0.75rem;
  }
}

/* Fullscreen logo styling - Always visible with highest priority */
.fullscreen-logo {
  border-radius: 12px;
  padding: 8px 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 9999 !important;
  pointer-events: none; /* Allow clicks to pass through */
}

.fullscreen-logo img {
  max-height: 8rem;
  min-width: 32px; /* Reserve space for logo */
  background: transparent;
}