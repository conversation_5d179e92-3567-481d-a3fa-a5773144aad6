'use client';

import { useState, useEffect } from 'react';
import VideoGrid, { GridLayout } from '@/components/VideoGrid';
import FullscreenVideoGrid from '@/components/FullscreenVideoGrid';
import GridLayoutControls from '@/components/GridLayoutControls';
import ConfigManager from '@/components/ConfigManager';
import StreamMonitor from '@/components/StreamMonitor';
import TabVisibilityDemo from '@/components/TabVisibilityDemo';
import { StreamConfig, AppSettings } from '@/app/api/config/route';

export default function Home() {
  const [streams, setStreams] = useState<StreamConfig[]>([]);
  const [settings, setSettings] = useState<AppSettings>({
    defaultLayout: '2x2',
    autoStartFullscreen: true,
    showManualControls: false,
    refreshInterval: 30000
  });
  const [gridLayout, setGridLayout] = useState<GridLayout>('2x2');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showConfigManager, setShowConfigManager] = useState(false);
  const [showStreamMonitor, setShowStreamMonitor] = useState(false);
  const [showTabVisibilityDemo, setShowTabVisibilityDemo] = useState(false);
  const [loading, setLoading] = useState(true);

  // Load configuration on component mount
  useEffect(() => {
    loadConfiguration();
  }, []);

  // Auto-start fullscreen if configured
  useEffect(() => {
    if (!loading && settings.autoStartFullscreen) {
      setIsFullscreen(true);
    }
  }, [loading, settings.autoStartFullscreen]);

  const loadConfiguration = async () => {
    try {
      const response = await fetch('/api/config');
      const data = await response.json();

      if (data.success) {
        setStreams(data.streams);
        setSettings(data.settings);
        setGridLayout(data.settings.defaultLayout as GridLayout);
      }
    } catch (error) {
      console.error('Failed to load configuration:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConfigUpdate = (newStreams: StreamConfig[], newSettings: AppSettings) => {
    setStreams(newStreams);
    setSettings(newSettings);
    setGridLayout(newSettings.defaultLayout as GridLayout);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const exitFullscreen = () => {
    setIsFullscreen(false);
  };

  // Show loading screen
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-black">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-xl">Loading Surveillance Dashboard...</p>
          <p className="text-sm text-gray-400 mt-2">Initializing camera streams</p>
        </div>
      </div>
    );
  }

  // Create a single VideoGrid component that will be reused
  const videoGridComponent = streams.length > 0 ? (
    <VideoGrid
      streams={streams}
      layout={gridLayout}
      isFullscreen={isFullscreen}
    />
  ) : (
    <div className="text-center p-12 bg-gray-100 dark:bg-gray-800 rounded-lg">
      <p>Add camera streams to get started</p>
    </div>
  );

  return (
    <>
      {/* Configuration Manager */}
      {showConfigManager && (
        <ConfigManager
          onClose={() => setShowConfigManager(false)}
          onConfigUpdate={handleConfigUpdate}
        />
      )}

      {/* Fullscreen Mode */}
      {isFullscreen && (
        <FullscreenVideoGrid
          streams={streams}
          layout={gridLayout}
          onLayoutChange={setGridLayout}
          onExitFullscreen={exitFullscreen}
          videoGridComponent={videoGridComponent}
          onShowConfig={() => setShowConfigManager(true)}
        />
      )}

      {/* Normal Mode */}
      <div className={`min-h-screen p-8 ${isFullscreen ? 'hidden' : ''}`}>
        <header className="mb-8 flex justify-between items-center">
          <div className="logo-container">
            <img
              src="/images/logo_white.png"
              alt="Incubase Logo"
              className="h-12 w-auto object-contain"
            />
            <div>
              <h1 className="text-2xl font-bold">Video Surveillance Dashboard</h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">Powered by Incubase</p>
            </div>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowTabVisibilityDemo(true)}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            >
              👁️ Tab Visibility Demo
            </button>
            <button
              onClick={() => setShowStreamMonitor(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              📊 Performance Monitor
            </button>
            <button
              onClick={() => setShowConfigManager(true)}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              ⚙️ Configure Streams
            </button>
          </div>
        </header>

        {/* Manual Controls - Only show if enabled in settings */}
        {settings.showManualControls && (
          <div className="mb-8 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h2 className="text-lg font-semibold mb-4">Manual Stream Management</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Note: Use the &quot;Configure Streams&quot; button above to manage streams via JSON configuration.
            </p>

            {streams.length > 0 && (
              <div className="mb-4">
                <h3 className="text-md font-medium mb-2">Active Cameras</h3>
                <div className="space-y-2">
                  {streams.map(stream => (
                    <div key={stream.id} className="flex justify-between items-center p-2 bg-white dark:bg-gray-700 rounded">
                      <span className="text-sm">
                        <strong>{stream.name}</strong> ({stream.type}): {stream.url}
                      </span>
                      <span className="text-xs text-green-600">Active</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

      {/* Grid Layout Controls */}
      {streams.length > 0 && (
        <div className="mb-6">
          <GridLayoutControls
            currentLayout={gridLayout}
            onLayoutChange={setGridLayout}
            isFullscreen={isFullscreen}
            onToggleFullscreen={toggleFullscreen}
            streamCount={streams.length}
          />
        </div>
      )}

      {/* Video Grid */}
      {!isFullscreen && videoGridComponent}
      </div>

      {/* Configuration Manager Modal */}
      {showConfigManager && (
        <ConfigManager
          onClose={() => setShowConfigManager(false)}
          onConfigUpdate={loadConfiguration}
        />
      )}

      {/* Stream Performance Monitor Modal */}
      {showStreamMonitor && (
        <StreamMonitor
          onClose={() => setShowStreamMonitor(false)}
        />
      )}

      {/* Tab Visibility Demo Modal */}
      {showTabVisibilityDemo && (
        <TabVisibilityDemo
          onClose={() => setShowTabVisibilityDemo(false)}
        />
      )}
    </>
  );
}
