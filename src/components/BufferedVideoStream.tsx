'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';

interface BufferedVideoStreamProps {
  url: string;
  name: string;
}

export default function BufferedVideoStream({ url, name }: BufferedVideoStreamProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamIdRef = useRef<string | null>(null);
  const hlsUrlRef = useRef<string | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [streamStatus, setStreamStatus] = useState<'loading' | 'buffering' | 'connected' | 'error' | 'paused'>('loading');
  const [isTabVisible, setIsTabVisible] = useState(true);
  const [bufferProgress, setBufferProgress] = useState(0);

  const setupVideoPlayer = useCallback((hlsUrl: string) => {
    if (videoRef.current) {
      const video = videoRef.current;
      console.log(`Setting up buffered video player for ${hlsUrl}`);
      video.src = hlsUrl;
      
      video.onloadstart = () => {
        console.log('Buffered video started loading');
        setStreamStatus('connected');
      };
      
      video.onerror = () => {
        console.error('Buffered video error');
        setStreamStatus('error');
      };
      
      video.oncanplay = () => {
        console.log('Buffered video can play');
        video.play().catch(console.error);
      };

      video.onwaiting = () => {
        console.log('Buffered video waiting for data');
        setStreamStatus('buffering');
      };

      video.onplaying = () => {
        console.log('Buffered video playing');
        setStreamStatus('connected');
      };
    }
  }, []);

  const pauseStream = useCallback(() => {
    if (videoRef.current) {
      const video = videoRef.current;
      console.log('Pausing buffered stream');
      video.pause();
      setStreamStatus('paused');
    }
  }, []);

  const resumeStream = useCallback(() => {
    if (hlsUrlRef.current && isTabVisible) {
      console.log('Resuming buffered stream with URL:', hlsUrlRef.current);
      if (videoRef.current) {
        videoRef.current.play().catch(console.error);
        setStreamStatus('connected');
      }
    }
  }, [isTabVisible]);

  // Tab visibility management
  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden;
      setIsTabVisible(isVisible);
      
      if (isVisible) {
        console.log(`Tab became visible - resuming buffered stream ${streamIdRef.current}`);
        // Clear any pending reconnect timeout
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
        // Resume after a short delay to ensure tab is fully active
        reconnectTimeoutRef.current = setTimeout(() => {
          resumeStream();
        }, 500);
      } else {
        console.log(`Tab became hidden - pausing buffered stream ${streamIdRef.current}`);
        pauseStream();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [resumeStream, pauseStream]);

  const startBufferedStream = useCallback(async () => {
    try {
      setStreamStatus('loading');
      setBufferProgress(0);

      // Start the buffered stream
      const response = await fetch(`/api/buffered-stream?url=${encodeURIComponent(url)}&name=${encodeURIComponent(name)}`);
      const data = await response.json();

      if (data.success && data.hlsUrl) {
        streamIdRef.current = data.streamId;
        hlsUrlRef.current = data.hlsUrl;
        
        console.log(`Buffered stream started with ID: ${data.streamId}, HLS URL: ${data.hlsUrl}`);
        
        // Show buffering status for 20 seconds
        setStreamStatus('buffering');
        
        // Simulate buffer progress
        let progress = 0;
        const progressInterval = setInterval(() => {
          progress += 5;
          setBufferProgress(progress);
          if (progress >= 100) {
            clearInterval(progressInterval);
          }
        }, 1000); // Update every second for 20 seconds
        
        // Start trying to play after buffer delay
        setTimeout(() => {
          if (isTabVisible) {
            setupVideoPlayer(data.hlsUrl);
          } else {
            setStreamStatus('paused');
          }
        }, 20000); // 20 second delay
        
      } else {
        console.error('Failed to start buffered stream:', data);
        setStreamStatus('error');
      }
    } catch (error) {
      console.error('Failed to start buffered stream:', error);
      setStreamStatus('error');
    }
  }, [url, name, isTabVisible, setupVideoPlayer]);

  // Initial stream setup
  useEffect(() => {
    let mounted = true;

    const initializeStream = async () => {
      if (mounted) {
        await startBufferedStream();
      }
    };

    initializeStream();

    return () => {
      mounted = false;
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      // Release stream when component unmounts
      if (streamIdRef.current) {
        fetch(`/api/buffered-stream?streamId=${streamIdRef.current}`, { method: 'DELETE' }).catch(console.error);
      }
    };
  }, [url, name, startBufferedStream]);

  // Handle tab visibility changes for existing streams
  useEffect(() => {
    if (streamIdRef.current && hlsUrlRef.current) {
      if (isTabVisible && streamStatus === 'paused') {
        console.log('Tab visible and buffered stream paused - resuming');
        resumeStream();
      } else if (!isTabVisible && (streamStatus === 'connected' || streamStatus === 'buffering')) {
        console.log('Tab hidden and buffered stream active - pausing');
        pauseStream();
      }
    }
  }, [isTabVisible]);

  return (
    <div className="relative w-full h-full bg-black rounded-lg overflow-hidden">
      {/* Video element */}
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        controls={false}
        muted
        playsInline
      />

      {/* Status overlay */}
      {streamStatus === 'loading' && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/70">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p>Starting buffered stream...</p>
          </div>
        </div>
      )}

      {streamStatus === 'buffering' && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/70">
          <div className="text-white text-center">
            <div className="text-4xl mb-2">📹</div>
            <p className="mb-2">Buffering Stream</p>
            <div className="w-48 bg-gray-700 rounded-full h-2 mb-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-1000"
                style={{ width: `${bufferProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-300">
              {bufferProgress}% - Building 20s buffer for stable playback
            </p>
          </div>
        </div>
      )}

      {streamStatus === 'paused' && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900/70">
          <div className="text-white text-center">
            <div className="text-4xl mb-2">⏸️</div>
            <p>Buffered Stream Paused</p>
            <p className="text-sm text-gray-300">Tab is not active</p>
          </div>
        </div>
      )}
      
      {streamStatus === 'error' && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-900/70">
          <div className="text-white text-center">
            <p>⚠️ Buffered Stream Error</p>
            <p className="text-sm">Check RTSP URL or FFmpeg</p>
          </div>
        </div>
      )}

      {/* Camera name overlay - Always on top with high z-index */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent p-3 camera-name-overlay z-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-white font-semibold text-base camera-label">
              {name}
            </span>
            <span className={`ml-2 inline-block w-2.5 h-2.5 rounded-full shadow-lg status-indicator ${
              streamStatus === 'connected' ? 'bg-green-400 connected' :
              streamStatus === 'loading' ? 'bg-yellow-400' : 
              streamStatus === 'buffering' ? 'bg-blue-400' :
              streamStatus === 'paused' ? 'bg-gray-400' : 'bg-red-400'
            }`}></span>
          </div>
          <div className="text-xs text-gray-200 font-mono font-medium camera-label">
            {streamStatus === 'connected' ? 'LIVE (-20s)' :
             streamStatus === 'loading' ? 'STARTING' : 
             streamStatus === 'buffering' ? 'BUFFERING' :
             streamStatus === 'paused' ? 'PAUSED' : 'OFFLINE'}
          </div>
        </div>
      </div>
    </div>
  );
}
