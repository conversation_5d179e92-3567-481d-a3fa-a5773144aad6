'use client';

import React, { useEffect, useRef } from 'react';
import { GridLayout } from './VideoGrid';
import GridLayoutControls from './GridLayoutControls';

interface FullscreenVideoGridProps {
  streams: {
    id: string;
    url: string;
    name: string;
    type: 'websocket' | 'hls';
  }[];
  layout: GridLayout;
  onLayoutChange: (layout: GridLayout) => void;
  onExitFullscreen: () => void;
  videoGridComponent: React.ReactNode;
  onShowConfig?: () => void;
}

export default function FullscreenVideoGrid({
  streams,
  layout,
  onLayoutChange,
  onExitFullscreen,
  videoGridComponent,
  onShowConfig
}: FullscreenVideoGridProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle escape key to exit fullscreen
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onExitFullscreen();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [onExitFullscreen]);

  // Auto-hide controls after inactivity
  const [showControls, setShowControls] = React.useState(true);
  const hideControlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const resetHideControlsTimer = () => {
    setShowControls(true);
    if (hideControlsTimeoutRef.current) {
      clearTimeout(hideControlsTimeoutRef.current);
    }
    hideControlsTimeoutRef.current = setTimeout(() => {
      setShowControls(false);
    }, 3000); // Hide after 3 seconds of inactivity
  };

  useEffect(() => {
    resetHideControlsTimer();
    return () => {
      if (hideControlsTimeoutRef.current) {
        clearTimeout(hideControlsTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 z-50 bg-black flex flex-col"
      onMouseMove={resetHideControlsTimer}
      onMouseEnter={resetHideControlsTimer}
    >
      {/* Logo - Always visible */}
      <div className="top-4 left-4 z-20 items-center gap-3 fullscreen-logo">
        <img
          src="/images/logo_white.png"
          alt="Incubase Logo"
          className="h-10 w-auto object-contain"
        />
      </div>

      {/* Controls Bar */}
      <div
        className={`absolute top-0 left-0 right-0 z-10 transition-transform duration-300 ${
          showControls ? 'translate-y-0' : '-translate-y-full'
        }`}
      >
        <div className="p-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-6">
              {/* Spacer for logo area */}
              <div className="w-80"></div>
              <GridLayoutControls
                currentLayout={layout}
                onLayoutChange={onLayoutChange}
                isFullscreen={true}
                onToggleFullscreen={onExitFullscreen}
                streamCount={streams.length}
              />
            </div>
            {onShowConfig && (
              <button
                onClick={onShowConfig}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                title="Configure Streams"
              >
                ⚙️ Config
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Video Grid */}
      <div className="flex-1 p-1 pt-1">
        {videoGridComponent}
      </div>

      {/* Instructions overlay (shows briefly) */}
      <div
        className={`absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/70 text-white px-4 py-2 rounded-lg text-sm transition-opacity duration-300 ${
          showControls ? 'opacity-100' : 'opacity-0'
        }`}
      >
        Press ESC to exit fullscreen • Move mouse to show controls • Click Config to edit streams
      </div>
    </div>
  );
}
