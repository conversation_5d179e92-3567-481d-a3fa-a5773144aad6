'use client';

import React from 'react';
import VideoStream from './VideoStream';
import HLSVideoStream from './HLSVideoStream';
import BufferedVideoStream from './BufferedVideoStream';

export type GridLayout = '1x1' | '2x1' | '1x2' | '2x2' | '3x3' | '4x4';

interface VideoGridProps {
  streams: {
    id: string;
    url: string;
    name: string;
    type: 'websocket' | 'hls' | 'buffered';
  }[];
  layout?: GridLayout;
  isFullscreen?: boolean;
}

const getGridClasses = (layout: GridLayout) => {
  switch (layout) {
    case '1x1':
      return 'grid-cols-1 grid-rows-1';
    case '2x1':
      return 'grid-cols-2 grid-rows-1';
    case '1x2':
      return 'grid-cols-1 grid-rows-2';
    case '2x2':
      return 'grid-cols-2 grid-rows-2';
    case '3x3':
      return 'grid-cols-3 grid-rows-3';
    case '4x4':
      return 'grid-cols-4 grid-rows-4';
    default:
      return 'grid-cols-2 grid-rows-2';
  }
};

const getMaxStreams = (layout: GridLayout): number => {
  switch (layout) {
    case '1x1': return 1;
    case '2x1': return 2;
    case '1x2': return 2;
    case '2x2': return 4;
    case '3x3': return 9;
    case '4x4': return 16;
    default: return 4;
  }
};

export default function VideoGrid({ streams, layout = '2x2', isFullscreen = false }: VideoGridProps) {
  const gridClasses = getGridClasses(layout);
  const maxStreams = getMaxStreams(layout);
  const displayStreams = streams.slice(0, maxStreams);

  // Fill empty slots with placeholder divs to maintain grid structure
  const emptySlots = Math.max(0, maxStreams - displayStreams.length);
  const emptyElements = Array(emptySlots).fill(null);

  const containerClasses = isFullscreen
    ? `grid ${gridClasses} gap-2 w-full h-full`
    : `grid ${gridClasses} gap-4 w-full`;

  return (
    <div className={containerClasses}>
      {displayStreams.map((stream) => (
        <div
          key={stream.id}
          className={`${isFullscreen ? 'aspect-auto' : 'aspect-video'} bg-black/10 rounded-lg overflow-hidden`}
        >
          {stream.type === 'hls' ? (
            <HLSVideoStream
              key={stream.id}
              url={stream.url}
              name={stream.name}
            />
          ) : stream.type === 'buffered' ? (
            <BufferedVideoStream
              key={stream.id}
              url={stream.url}
              name={stream.name}
            />
          ) : (
            <VideoStream
              key={stream.id}
              url={stream.url}
              name={stream.name}
            />
          )}
        </div>
      ))}
      {emptyElements.map((_, index) => (
        <div
          key={`empty-${index}`}
          className={`${isFullscreen ? 'aspect-auto' : 'aspect-video'} bg-black/5 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center`}
        >
          <span className="text-gray-400 text-sm">Empty Slot</span>
        </div>
      ))}
    </div>
  );
}
