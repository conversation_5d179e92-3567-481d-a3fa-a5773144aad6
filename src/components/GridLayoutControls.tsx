'use client';

import React from 'react';
import { GridLayout } from './VideoGrid';

interface GridLayoutControlsProps {
  currentLayout: GridLayout;
  onLayoutChange: (layout: GridLayout) => void;
  isFullscreen: boolean;
  onToggleFullscreen: () => void;
  streamCount: number;
}

const layoutOptions: { value: GridLayout; label: string; maxStreams: number }[] = [
  { value: '1x1', label: '1×1', maxStreams: 1 },
  { value: '2x1', label: '2×1', maxStreams: 2 },
  { value: '1x2', label: '1×2', maxStreams: 2 },
  { value: '2x2', label: '2×2', maxStreams: 4 },
  { value: '3x3', label: '3×3', maxStreams: 9 },
  { value: '4x4', label: '4×4', maxStreams: 16 },
];

export default function GridLayoutControls({
  currentLayout,
  onLayoutChange,
  isFullscreen,
  onToggleFullscreen,
  streamCount
}: GridLayoutControlsProps) {
  return (
    <div className="flex items-center gap-4 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
      {/* Layout Selection */}
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Grid Layout:
        </span>
        <div className="flex gap-1">
          {layoutOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => onLayoutChange(option.value)}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                currentLayout === option.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
              title={`${option.label} layout (max ${option.maxStreams} streams)`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Stream Count Info */}
      <div className="text-sm text-gray-600 dark:text-gray-400">
        Streams: {streamCount}
      </div>

      {/* Fullscreen Toggle */}
      <button
        onClick={onToggleFullscreen}
        className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
          isFullscreen
            ? 'bg-red-600 hover:bg-red-700 text-white'
            : 'bg-green-600 hover:bg-green-700 text-white'
        }`}
      >
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          {isFullscreen ? (
            // Exit fullscreen icon
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 9V4.5M9 9H4.5M9 9L3.5 3.5M15 9h4.5M15 9V4.5M15 9l5.5-5.5M9 15v4.5M9 15H4.5M9 15l-5.5 5.5M15 15h4.5M15 15v4.5m0-4.5l5.5 5.5"
            />
          ) : (
            // Enter fullscreen icon
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
            />
          )}
        </svg>
        {isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
      </button>
    </div>
  );
}
