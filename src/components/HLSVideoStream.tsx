'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';

interface HLSVideoStreamProps {
  url: string;
  name: string;
}

export default function HLSVideoStream({ url, name }: HLSVideoStreamProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamIdRef = useRef<string | null>(null);
  const hlsUrlRef = useRef<string | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [streamStatus, setStreamStatus] = useState<'loading' | 'connected' | 'error' | 'paused'>('loading');
  const [isTabVisible, setIsTabVisible] = useState(true);

  const setupVideoPlayer = useCallback((hlsUrl: string) => {
    if (videoRef.current) {
      const video = videoRef.current;
      console.log(`Setting up HLS video player for ${hlsUrl}`);
      video.src = hlsUrl;

      video.onloadstart = () => {
        console.log('HLS video started loading');
        setStreamStatus('connected');
      };

      video.onerror = () => {
        console.error('HLS video error');
        setStreamStatus('error');
      };

      video.oncanplay = () => {
        console.log('HLS video can play');
        video.play().catch(console.error);
      };
    }
  }, []);

  const pauseStream = useCallback(() => {
    if (videoRef.current) {
      const video = videoRef.current;
      console.log('Pausing HLS stream');
      video.pause();
      video.src = '';
      setStreamStatus('paused');
    }
  }, []);

  const resumeStream = useCallback(() => {
    if (hlsUrlRef.current && isTabVisible) {
      console.log('Resuming HLS stream with URL:', hlsUrlRef.current);
      setStreamStatus('loading');
      setupVideoPlayer(hlsUrlRef.current);
    }
  }, [isTabVisible, setupVideoPlayer]);

  // Tab visibility management
  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden;
      setIsTabVisible(isVisible);

      if (isVisible) {
        console.log(`Tab became visible - resuming HLS stream ${streamIdRef.current}`);
        // Clear any pending reconnect timeout
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
        // Resume after a short delay to ensure tab is fully active
        reconnectTimeoutRef.current = setTimeout(() => {
          resumeStream();
        }, 500);
      } else {
        console.log(`Tab became hidden - pausing HLS stream ${streamIdRef.current}`);
        pauseStream();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [resumeStream, pauseStream]);

  const startHLSStream = useCallback(async () => {
    try {
      setStreamStatus('loading');

      // Start the HLS stream conversion
      const response = await fetch(`/api/hls-stream?url=${encodeURIComponent(url)}&name=${encodeURIComponent(name)}`);
      const data = await response.json();

      if (data.success && data.hlsUrl) {
        streamIdRef.current = data.streamId;
        hlsUrlRef.current = data.hlsUrl;

        console.log(`HLS stream started with ID: ${data.streamId}, HLS URL: ${data.hlsUrl}`);

        // Only set up video player if tab is visible
        if (isTabVisible) {
          setupVideoPlayer(data.hlsUrl);
        } else {
          setStreamStatus('paused');
        }
      } else {
        console.error('Failed to start HLS stream:', data);
        setStreamStatus('error');
      }
    } catch (error) {
      console.error('Failed to start HLS stream:', error);
      setStreamStatus('error');
    }
  }, [url, name, isTabVisible, setupVideoPlayer]);

  // Initial stream setup
  useEffect(() => {
    let mounted = true;

    const initializeStream = async () => {
      if (mounted) {
        await startHLSStream();
      }
    };

    initializeStream();

    return () => {
      mounted = false;
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      // Release stream when component unmounts (don't delete, just reduce client count)
      if (streamIdRef.current) {
        fetch(`/api/hls-stream/release?streamId=${streamIdRef.current}`, { method: 'POST' }).catch(console.error);
      }
    };
  }, [url, name, startHLSStream]);

  // Handle tab visibility changes for existing streams
  useEffect(() => {
    if (streamIdRef.current && hlsUrlRef.current) {
      if (isTabVisible && streamStatus === 'paused') {
        console.log('Tab visible and HLS stream paused - resuming');
        resumeStream();
      } else if (!isTabVisible && (streamStatus === 'connected' || streamStatus === 'loading')) {
        console.log('Tab hidden and HLS stream active - pausing');
        pauseStream();
      }
    }
  }, [isTabVisible, pauseStream, resumeStream, streamStatus]);

  return (
    <div className="relative h-full w-full bg-black rounded-lg overflow-hidden">
      <video
        ref={videoRef}
        className="h-full w-full object-cover"
        controls={false}
        muted
        autoPlay
        playsInline
      />
      
      {/* Status overlay */}
      {streamStatus === 'loading' && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/70">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p>{isTabVisible ? 'Starting HLS stream...' : 'Resuming stream...'}</p>
          </div>
        </div>
      )}

      {streamStatus === 'paused' && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900/70">
          <div className="text-white text-center">
            <div className="text-4xl mb-2">⏸️</div>
            <p>HLS Stream Paused</p>
            <p className="text-sm text-gray-300">Tab is not active</p>
          </div>
        </div>
      )}

      {streamStatus === 'error' && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-900/70">
          <div className="text-white text-center">
            <p>⚠️ HLS Stream Error</p>
            <p className="text-sm">Check RTSP URL or FFmpeg</p>
          </div>
        </div>
      )}

      {/* Camera name overlay - Always on top with high z-index */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent p-3 camera-name-overlay z-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-white font-semibold text-base camera-label">
              {name}
            </span>
            <span className="ml-2 text-xs text-blue-300 font-mono font-medium camera-label bg-blue-900/50 px-1.5 py-0.5 rounded">HLS</span>
            <span className={`ml-2 inline-block w-2.5 h-2.5 rounded-full shadow-lg status-indicator ${
              streamStatus === 'connected' ? 'bg-green-400 connected' :
              streamStatus === 'loading' ? 'bg-yellow-400' :
              streamStatus === 'paused' ? 'bg-blue-400' : 'bg-red-400'
            }`}></span>
          </div>
          <div className="text-xs text-gray-200 font-mono font-medium camera-label">
            {streamStatus === 'connected' ? 'LIVE' :
             streamStatus === 'loading' ? 'CONNECTING' :
             streamStatus === 'paused' ? 'PAUSED' : 'OFFLINE'}
          </div>
        </div>
      </div>
    </div>
  );
}
