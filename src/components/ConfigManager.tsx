'use client';

import React, { useState, useEffect } from 'react';
import { StreamConfig, AppSettings, ConfigData } from '@/app/api/config/route';

interface ConfigManagerProps {
  onClose: () => void;
  onConfigUpdate: (streams: StreamConfig[], settings: AppSettings) => void;
}

export default function ConfigManager({ onClose, onConfigUpdate }: ConfigManagerProps) {
  const [config, setConfig] = useState<ConfigData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      const response = await fetch('/api/config');
      const data = await response.json();
      
      if (data.success) {
        // Load the full config including disabled streams
        const fullConfigResponse = await fetch('/config/streams.json');
        const fullConfig = await fullConfigResponse.json();
        setConfig(fullConfig);
      }
    } catch (error) {
      console.error('Failed to load configuration:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async () => {
    if (!config) return;
    
    setSaving(true);
    try {
      const response = await fetch('/api/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      const result = await response.json();
      if (result.success) {
        const enabledStreams = config.streams.filter(stream => stream.enabled);
        onConfigUpdate(enabledStreams, config.settings);
        onClose();
      }
    } catch (error) {
      console.error('Failed to save configuration:', error);
    } finally {
      setSaving(false);
    }
  };

  const updateStream = (index: number, field: keyof StreamConfig, value: string | boolean) => {
    if (!config) return;
    
    const newStreams = [...config.streams];
    newStreams[index] = { ...newStreams[index], [field]: value };
    setConfig({ ...config, streams: newStreams });
  };

  const addStream = () => {
    if (!config) return;
    
    const newStream: StreamConfig = {
      id: `camera-${Date.now()}`,
      name: 'New Camera',
      url: 'rtsp://username:password@192.168.1.100:554/stream1',
      type: 'websocket',
      enabled: true
    };
    
    setConfig({
      ...config,
      streams: [...config.streams, newStream]
    });
  };

  const removeStream = (index: number) => {
    if (!config) return;
    
    const newStreams = config.streams.filter((_, i) => i !== index);
    setConfig({ ...config, streams: newStreams });
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-center">Loading configuration...</p>
        </div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg">
          <p className="text-red-600">Failed to load configuration</p>
          <button onClick={onClose} className="mt-4 px-4 py-2 bg-gray-600 text-white rounded">
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">Stream Configuration</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-2xl"
            >
              ×
            </button>
          </div>

          {/* Settings */}
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">Settings</h3>
            <div className="grid grid-cols-2 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.settings.autoStartFullscreen}
                  onChange={(e) => setConfig({
                    ...config,
                    settings: { ...config.settings, autoStartFullscreen: e.target.checked }
                  })}
                  className="mr-2"
                />
                Auto Start Fullscreen
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.settings.showManualControls}
                  onChange={(e) => setConfig({
                    ...config,
                    settings: { ...config.settings, showManualControls: e.target.checked }
                  })}
                  className="mr-2"
                />
                Show Manual Controls
              </label>
            </div>
          </div>

          {/* Streams */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Camera Streams</h3>
              <button
                onClick={addStream}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Add Stream
              </button>
            </div>

            {config.streams.map((stream, index) => (
              <div key={stream.id} className="p-4 border rounded-lg space-y-3">
                <div className="flex justify-between items-center">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={stream.enabled}
                      onChange={(e) => updateStream(index, 'enabled', e.target.checked)}
                      className="mr-2"
                    />
                    <span className="font-medium">Enabled</span>
                  </label>
                  <button
                    onClick={() => removeStream(index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    Remove
                  </button>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Name</label>
                    <input
                      type="text"
                      value={stream.name}
                      onChange={(e) => updateStream(index, 'name', e.target.value)}
                      className="w-full p-2 border rounded"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Type</label>
                    <select
                      value={stream.type}
                      onChange={(e) => updateStream(index, 'type', e.target.value as 'websocket' | 'hls' | 'buffered')}
                      className="w-full p-2 border rounded"
                    >
                      <option value="websocket">WebSocket (JSMpeg) - Real-time</option>
                      <option value="hls">HLS (HTML5) - Low latency</option>
                      <option value="buffered">Buffered - Stable (20s delay)</option>
                    </select>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">RTSP URL</label>
                  <input
                    type="text"
                    value={stream.url}
                    onChange={(e) => updateStream(index, 'url', e.target.value)}
                    className="w-full p-2 border rounded"
                    placeholder="rtsp://username:password@192.168.1.100:554/stream1"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-4 mt-6 pt-6 border-t">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              onClick={saveConfig}
              disabled={saving}
              className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {saving ? 'Saving...' : 'Save Configuration'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
