'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import JSMpeg from '@cycjimmy/jsmpeg-player';
import { connectionManager } from '@/lib/connectionManager';

interface VideoStreamProps {
  url: string;
  name: string;
}

export default function VideoStream({ url, name }: VideoStreamProps) {
  const videoRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<JSMpeg.VideoElement | null>(null);
  const streamIdRef = useRef<string | null>(null);
  const wsUrlRef = useRef<string | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [streamStatus, setStreamStatus] = useState<'loading' | 'connected' | 'error' | 'paused'>('loading');
  const [isTabVisible, setIsTabVisible] = useState(true);

  const createPlayer = useCallback((wsUrl: string) => {
    if (videoRef.current && !playerRef.current) {
      try {
        console.log(`Creating JSMpeg player for ${wsUrl}`);

        const connectionId = `${url}-${name}`;
        connectionManager.registerConnection(connectionId);

        playerRef.current = new JSMpeg.VideoElement(
          videoRef.current,
          wsUrl,
          {
            autoplay: true,
            loop: true,
            control: false,
            decodeFirstFrame: true,
            hooks: {
              load: () => {
                console.log('JSMpeg player loaded successfully');
                connectionManager.markConnected(connectionId);
                setStreamStatus('connected');
              }
            }
          },
          {
            audio: false,
            onSourceCompleted: () => {
              console.log('JSMpeg source completed - connection lost');
              connectionManager.markDisconnected(
                connectionId,
                'Stream source completed',
                async () => {
                  // Retry by recreating the player
                  if (playerRef.current) {
                    playerRef.current.destroy();
                    playerRef.current = null;
                  }
                  if (wsUrlRef.current) {
                    createPlayer(wsUrlRef.current);
                  }
                }
              );
              setStreamStatus('error');
            }
          }
        );
      } catch (error) {
        console.error('Failed to create JSMpeg player:', error);
        const connectionId = `${url}-${name}`;
        connectionManager.markDisconnected(
          connectionId,
          error instanceof Error ? error.message : 'Failed to create player',
          async () => {
            if (wsUrlRef.current) {
              createPlayer(wsUrlRef.current);
            }
          }
        );
        setStreamStatus('error');
      }
    }
  }, [url, name]);

  const pauseStream = useCallback(() => {
    if (playerRef.current) {
      try {
        console.log('Pausing stream - destroying JSMpeg player');
        playerRef.current.destroy();
        playerRef.current = null;
        setStreamStatus('paused');
      } catch (error) {
        console.error('Failed to pause stream:', error);
      }
    }
  }, []);

  const reconnectStream = useCallback(() => {
    if (wsUrlRef.current && isTabVisible) {
      console.log('Reconnecting stream with URL:', wsUrlRef.current);
      setStreamStatus('loading');
      createPlayer(wsUrlRef.current);
    }
  }, [isTabVisible, createPlayer]);

  // Tab visibility management
  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden;
      setIsTabVisible(isVisible);

      if (isVisible) {
        console.log(`Tab became visible - reconnecting stream ${streamIdRef.current}`);
        // Clear any pending reconnect timeout
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
        // Reconnect after a short delay to ensure tab is fully active
        reconnectTimeoutRef.current = setTimeout(() => {
          reconnectStream();
        }, 500);
      } else {
        console.log(`Tab became hidden - pausing stream ${streamIdRef.current}`);
        pauseStream();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [reconnectStream, pauseStream]);

  const startStream = useCallback(async () => {
    try {
      setStreamStatus('loading');

      // Start the stream conversion
      const response = await fetch(`/api/stream?url=${encodeURIComponent(url)}&name=${encodeURIComponent(name)}`);
      const data = await response.json();

      if (data.success && data.wsUrl) {
        streamIdRef.current = data.streamId;
        wsUrlRef.current = data.wsUrl;

        console.log(`Stream started with ID: ${data.streamId}, WS URL: ${data.wsUrl}`);

        // Only create player if tab is visible
        if (isTabVisible) {
          createPlayer(data.wsUrl);
        } else {
          setStreamStatus('paused');
        }
      } else {
        console.error('Failed to start stream:', data);
        setStreamStatus('error');
      }
    } catch (error) {
      console.error('Failed to start stream:', error);
      setStreamStatus('error');
    }
  }, [url, name, isTabVisible, createPlayer]);

  // Initial stream setup
  useEffect(() => {
    let mounted = true;

    const initializeStream = async () => {
      if (mounted) {
        await startStream();
      }
    };

    initializeStream();

    return () => {
      mounted = false;
      if (playerRef.current) {
        playerRef.current.destroy();
        playerRef.current = null;
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      // Clean up stream when component unmounts
      if (streamIdRef.current) {
        fetch(`/api/stream?streamId=${streamIdRef.current}`, { method: 'DELETE' }).catch(console.error);
      }
    };
  }, [url, name, startStream]);

  // Handle tab visibility changes for existing streams
  useEffect(() => {
    if (streamIdRef.current && wsUrlRef.current) {
      if (isTabVisible && streamStatus === 'paused') {
        console.log('Tab visible and stream paused - reconnecting');
        reconnectStream();
      } else if (!isTabVisible && (streamStatus === 'connected' || streamStatus === 'loading')) {
        console.log('Tab hidden and stream active - pausing');
        pauseStream();
      }
    }
  }, [isTabVisible]);

  return (
    <div className="relative h-full w-full bg-black rounded-lg overflow-hidden">
      <div ref={videoRef} className="h-full w-full"></div>

      {/* Status overlay */}
      {streamStatus === 'loading' && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/70">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p>{isTabVisible ? 'Starting stream...' : 'Reconnecting...'}</p>
          </div>
        </div>
      )}

      {streamStatus === 'paused' && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900/70">
          <div className="text-white text-center">
            <div className="text-4xl mb-2">⏸️</div>
            <p>Stream Paused</p>
            <p className="text-sm text-gray-300">Tab is not active</p>
          </div>
        </div>
      )}

      {streamStatus === 'error' && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-900/70">
          <div className="text-white text-center">
            <p>⚠️ Stream Error</p>
            <p className="text-sm">Check RTSP URL or FFmpeg</p>
          </div>
        </div>
      )}

      {/* Camera name overlay - Always on top with high z-index */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent p-3 camera-name-overlay z-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-white font-semibold text-base camera-label">
              {name}
            </span>
            <span className={`ml-2 inline-block w-2.5 h-2.5 rounded-full shadow-lg status-indicator ${
              streamStatus === 'connected' ? 'bg-green-400 connected' :
              streamStatus === 'loading' ? 'bg-yellow-400' :
              streamStatus === 'paused' ? 'bg-blue-400' : 'bg-red-400'
            }`}></span>
          </div>
          <div className="text-xs text-gray-200 font-mono font-medium camera-label">
            {streamStatus === 'connected' ? 'LIVE' :
             streamStatus === 'loading' ? 'CONNECTING' :
             streamStatus === 'paused' ? 'PAUSED' : 'OFFLINE'}
          </div>
        </div>
      </div>
    </div>
  );
}
