'use client';

import React, { useState, useEffect } from 'react';
import { useTabVisibility } from '@/hooks/useTabVisibility';

interface TabVisibilityDemoProps {
  onClose: () => void;
}

export default function TabVisibilityDemo({ onClose }: TabVisibilityDemoProps) {
  const isTabVisible = useTabVisibility();
  const [visibilityHistory, setVisibilityHistory] = useState<Array<{
    timestamp: string;
    status: 'visible' | 'hidden';
  }>>([]);

  useEffect(() => {
    const now = new Date().toLocaleTimeString();
    setVisibilityHistory(prev => [
      ...prev.slice(-9), // Keep last 10 entries
      {
        timestamp: now,
        status: isTabVisible ? 'visible' : 'hidden'
      }
    ]);
  }, [isTabVisible]);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Tab Visibility Enhancement Demo</h2>
            <button
              onClick={onClose}
              className="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Close
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* Current Status */}
          <div className="mb-8">
            <h3 className="text-xl font-bold mb-4">Current Tab Status</h3>
            <div className={`p-4 rounded-lg border-2 ${
              isTabVisible 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center gap-3">
                <div className={`w-4 h-4 rounded-full ${
                  isTabVisible ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span className="text-lg font-semibold">
                  Tab is {isTabVisible ? 'VISIBLE' : 'HIDDEN'}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                {isTabVisible 
                  ? 'Video streams are active and consuming resources'
                  : 'Video streams are paused to save resources'
                }
              </p>
            </div>
          </div>

          {/* How It Works */}
          <div className="mb-8">
            <h3 className="text-xl font-bold mb-4">How the Enhancement Works</h3>
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">🔍 Detection</h4>
                <p className="text-sm text-blue-700">
                  Uses the Page Visibility API to detect when browser tabs become active or inactive
                </p>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-2">⏸️ Auto-Pause</h4>
                <p className="text-sm text-green-700">
                  When tab becomes inactive: WebSocket connections are closed, video players are paused
                </p>
              </div>
              
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-800 mb-2">▶️ Auto-Resume</h4>
                <p className="text-sm text-purple-700">
                  When tab becomes active: Streams reconnect automatically with a 500ms delay
                </p>
              </div>
              
              <div className="bg-orange-50 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-2">💾 Resource Savings</h4>
                <p className="text-sm text-orange-700">
                  Reduces CPU, memory, and network usage when users aren&apos;t actively viewing streams
                </p>
              </div>
            </div>
          </div>

          {/* Test Instructions */}
          <div className="mb-8">
            <h3 className="text-xl font-bold mb-4">Test the Feature</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Keep this demo open and note the current status above</li>
                <li>Switch to another browser tab or minimize the window</li>
                <li>Wait a moment, then return to this tab</li>
                <li>Observe how the status changes and history is recorded</li>
                <li>Try this with video streams to see them pause and resume</li>
              </ol>
            </div>
          </div>

          {/* Visibility History */}
          <div className="mb-6">
            <h3 className="text-xl font-bold mb-4">Visibility History</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              {visibilityHistory.length > 0 ? (
                <div className="space-y-2">
                  {visibilityHistory.slice().reverse().map((entry, index) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="font-mono">{entry.timestamp}</span>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        entry.status === 'visible' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {entry.status.toUpperCase()}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center">No visibility changes recorded yet</p>
              )}
            </div>
          </div>

          {/* Benefits */}
          <div>
            <h3 className="text-xl font-bold mb-4">Performance Benefits</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">🔋 Battery Life</h4>
                <p className="text-sm text-blue-700">
                  Extends laptop/mobile battery life by reducing background processing
                </p>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-2">🌐 Network Usage</h4>
                <p className="text-sm text-green-700">
                  Reduces bandwidth consumption when streams aren&apos;t being viewed
                </p>
              </div>
              
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-800 mb-2">⚡ CPU Usage</h4>
                <p className="text-sm text-purple-700">
                  Lowers CPU usage by pausing video decoding and rendering
                </p>
              </div>
              
              <div className="bg-orange-50 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-2">🎯 User Experience</h4>
                <p className="text-sm text-orange-700">
                  Seamless pause/resume with visual feedback and status indicators
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
