# Simple Configuration Approach

## Overview
The surveillance system now uses a **simple direct JSON file approach** instead of complex API routes for loading configuration.

## How It Works

### **Loading Configuration (Simple)**
```javascript
// Direct JSON fetch - no API needed
const response = await fetch('/config/streams.json');
const config = await response.json();

// Filter enabled streams
const enabledStreams = config.streams.filter(stream => stream.enabled);
```

### **Saving Configuration (API Required)**
```javascript
// Browsers can't write files, so we still need API for saving
const response = await fetch('/api/config', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(config)
});
```

## File Structure
```
public/
  config/
    streams.json    ← Direct access via /config/streams.json
src/
  app/
    api/
      config/
        route.ts    ← POST-only API for saving
  types/
    config.ts       ← Shared TypeScript types
```

## Benefits

### ✅ **Advantages**
- **Simpler**: No complex API logic for reading
- **Faster**: Direct file access, no server processing
- **Cacheable**: Browser can cache the JSON file
- **Transparent**: Easy to debug and inspect
- **Deployment-friendly**: Works in all environments

### ⚠️ **Limitations**
- **Static**: Changes require file replacement or API save
- **No server-side processing**: No validation or filtering on load
- **Public**: Configuration file is publicly accessible

## Deployment

### **Development**
- File is served directly from `public/config/streams.json`
- Changes are reflected immediately

### **Production**
- File is included in the build output
- Accessible at `https://yourdomain.com/config/streams.json`
- Can be updated via the ConfigManager UI (uses POST API)

## Configuration File Format
```json
{
  "streams": [
    {
      "id": "camera-1",
      "name": "Camera 1",
      "url": "rtsp://...",
      "type": "websocket",
      "enabled": true
    }
  ],
  "settings": {
    "defaultLayout": "2x2",
    "autoStartFullscreen": false,
    "showManualControls": true,
    "refreshInterval": 30000
  }
}
```

## Error Handling
- **File not found**: Falls back to empty streams array and default settings
- **Invalid JSON**: Logs error and uses fallback configuration
- **Network error**: Graceful degradation with default values

## API Endpoints

### **GET /config/streams.json**
- Direct file access
- Returns raw JSON configuration
- Cached by browser

### **POST /api/config**
- Saves configuration to file
- Validates JSON structure
- Creates directory if needed

### **GET /api/config**
- Returns 405 with message to use direct JSON access
- Kept for backward compatibility

## Migration Benefits
This approach resolves the deployment issue where `/api/config` returned 405 errors because:

1. **No file system complexity**: Direct HTTP access works everywhere
2. **No path resolution issues**: Standard web server file serving
3. **No environment dependencies**: Works in Docker, serverless, traditional hosting
4. **Simpler debugging**: Easy to test with curl or browser

## Testing
```bash
# Test direct JSON access
curl https://yourdomain.com/config/streams.json

# Test save API
curl -X POST https://yourdomain.com/api/config \
  -H "Content-Type: application/json" \
  -d '{"streams":[],"settings":{}}'
```

This simple approach eliminates the 405 error and provides a more reliable, deployment-friendly configuration system.
