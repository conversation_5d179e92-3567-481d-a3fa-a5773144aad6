# Fish Tank Surveillance Dashboard - Packaging Script (Windows PowerShell)
# Usage: .\package.ps1 [version] [-IncludeBuild]

param(
    [string]$Version = "latest",
    [switch]$IncludeBuild
)

# Configuration
$ProjectName = "fish-tank-surveillance"
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$ArchiveName = "${ProjectName}_${Version}_${Timestamp}.tar.gz"
$TempDir = ".\package_temp"

# Colors for output
function Write-Status {
    param([string]$Message)
    Write-Host "✓ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠ $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "✗ $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ $Message" -ForegroundColor Cyan
}

Write-Host "🐟 Fish Tank Surveillance Dashboard - Packaging Script" -ForegroundColor Blue
Write-Host "====================================================" -ForegroundColor Blue

# Check if tar is available (Windows 10 1903+ has built-in tar)
try {
    $null = Get-Command tar -ErrorAction Stop
    Write-Status "tar command found"
} catch {
    Write-Error "tar command not found. Please install tar or use Windows 10 1903+."
    exit 1
}

# Clean up any existing temp directory
if (Test-Path $TempDir) {
    Write-Info "Cleaning up existing temp directory..."
    Remove-Item -Recurse -Force $TempDir
}

# Create temp directory
New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
Write-Status "Created temporary directory: $TempDir"

# Copy project files
Write-Info "Copying project files..."

# Essential directories
$EssentialDirs = @("src", "public")
foreach ($dir in $EssentialDirs) {
    if (Test-Path $dir) {
        Copy-Item -Recurse $dir "$TempDir\"
        Write-Status "Copied $dir/"
    } else {
        Write-Warning "$dir/ not found"
    }
}

# Essential files
$EssentialFiles = @(
    "package.json",
    "package-lock.json",
    "next.config.ts",
    "tsconfig.json",
    "tailwind.config.ts",
    "postcss.config.mjs",
    "eslint.config.mjs"
)

foreach ($file in $EssentialFiles) {
    if (Test-Path $file) {
        Copy-Item $file "$TempDir\"
        Write-Status "Copied $file"
    } else {
        Write-Warning "$file not found"
    }
}

# Docker files
$DockerFiles = @(
    "Dockerfile",
    "docker-compose.yml",
    "docker-compose.prod.yml",
    "docker-build.sh",
    ".dockerignore"
)

foreach ($file in $DockerFiles) {
    if (Test-Path $file) {
        Copy-Item $file "$TempDir\"
        Write-Status "Copied $file"
    } else {
        Write-Warning "$file not found"
    }
}

# Documentation
$DocFiles = @("README.md", "FFMPEG_SETUP.md", ".gitignore")
foreach ($file in $DocFiles) {
    if (Test-Path $file) {
        Copy-Item $file "$TempDir\"
        Write-Status "Copied $file"
    } else {
        Write-Warning "$file not found"
    }
}

# Include build files if requested
if ($IncludeBuild) {
    if (Test-Path ".next") {
        Write-Info "Including build files..."
        Copy-Item -Recurse ".next" "$TempDir\"
        Write-Status "Build files included"
    } else {
        Write-Warning "No build files found. Run 'npm run build' first."
    }
}

# Create version info file
$GitCommit = try { git rev-parse HEAD 2>$null } catch { "Not available" }
$GitBranch = try { git branch --show-current 2>$null } catch { "Not available" }

$VersionInfo = @"
Fish Tank Surveillance Dashboard
================================

Version: $Version
Build Date: $(Get-Date)
Build Host: $env:COMPUTERNAME
Build User: $env:USERNAME
Git Commit: $GitCommit
Git Branch: $GitBranch

Package Contents:
- Source code (src/)
- Public assets (public/)
- Configuration files
- Docker setup
- Documentation

Installation:
1. Extract the archive
2. Run: npm install
3. Configure streams in public/config/streams.json
4. Build: npm run build
5. Start: npm start

Docker Deployment:
1. Extract the archive
2. Run: ./docker-build.sh (Linux/Mac) or docker-compose up (Windows)
3. Access: http://localhost:3000
"@

$VersionInfo | Out-File -FilePath "$TempDir\VERSION_INFO.txt" -Encoding UTF8
Write-Status "Version info created"

# Create Windows installation script
$InstallScript = @'
@echo off
echo 🐟 Fish Tank Surveillance Dashboard - Installation
echo ================================================

REM Check Node.js
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ Node.js not found. Please install Node.js 18+ first.
    pause
    exit /b 1
)

for /f "tokens=1 delims=v" %%i in ('node -v') do set NODE_VERSION=%%i
echo ✅ Node.js %NODE_VERSION% found

REM Install dependencies
echo 📦 Installing dependencies...
call npm install
if %ERRORLEVEL% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

REM Create directories
if not exist "public\streams" mkdir "public\streams"
if not exist "logs" mkdir "logs"

echo 🔧 Setting up configuration...
if not exist "public\config\streams.json" (
    echo ⚠️  Please configure your streams in public\config\streams.json
)

echo 🏗️  Building application...
call npm run build
if %ERRORLEVEL% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo ✅ Installation complete!
echo.
echo Next steps:
echo 1. Configure streams: edit public\config\streams.json
echo 2. Start application: npm start
echo 3. Access dashboard: http://localhost:3000
pause
'@

$InstallScript | Out-File -FilePath "$TempDir\install.bat" -Encoding ASCII
Write-Status "Windows installation script created"

# Create PowerShell installation script
$PSInstallScript = @'
# Fish Tank Surveillance Dashboard - PowerShell Installation Script

Write-Host "🐟 Fish Tank Surveillance Dashboard - Installation" -ForegroundColor Blue
Write-Host "================================================" -ForegroundColor Blue

# Check Node.js
try {
    $nodeVersion = node -v
    Write-Host "✅ Node.js $nodeVersion found" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js 18+ first." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Cyan
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Create directories
if (!(Test-Path "public\streams")) { New-Item -ItemType Directory -Path "public\streams" -Force }
if (!(Test-Path "logs")) { New-Item -ItemType Directory -Path "logs" -Force }

Write-Host "🔧 Setting up configuration..." -ForegroundColor Cyan
if (!(Test-Path "public\config\streams.json")) {
    Write-Host "⚠️  Please configure your streams in public\config\streams.json" -ForegroundColor Yellow
}

Write-Host "🏗️  Building application..." -ForegroundColor Cyan
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Installation complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Configure streams: edit public\config\streams.json"
Write-Host "2. Start application: npm start"
Write-Host "3. Access dashboard: http://localhost:3000"
Read-Host "Press Enter to continue"
'@

$PSInstallScript | Out-File -FilePath "$TempDir\install.ps1" -Encoding UTF8
Write-Status "PowerShell installation script created"

# Create the tar archive
Write-Info "Creating tar archive: $ArchiveName"
Set-Location $TempDir
tar -czf "..\$ArchiveName" *
Set-Location ..

# Get archive size
$ArchiveSize = [math]::Round((Get-Item $ArchiveName).Length / 1MB, 2)
Write-Status "Archive created: $ArchiveName ($ArchiveSize MB)"

# Clean up temp directory
Remove-Item -Recurse -Force $TempDir
Write-Status "Cleaned up temporary files"

# Show archive contents
Write-Info "Archive contents:"
$contents = tar -tzf $ArchiveName
$contents | Select-Object -First 20
if ($contents.Count -gt 20) {
    Write-Host "... and $($contents.Count - 20) more files"
}

Write-Host ""
Write-Host "🎉 Package created successfully!" -ForegroundColor Green
Write-Host "Archive: $ArchiveName" -ForegroundColor Blue
Write-Host "Size: $ArchiveSize MB" -ForegroundColor Blue
Write-Host ""
Write-Host "Usage:" -ForegroundColor Yellow
Write-Host "  Extract: tar -xzf $ArchiveName"
Write-Host "  Install: cd extracted_folder && .\install.bat (or .\install.ps1)"
Write-Host "  Docker:  cd extracted_folder && docker-compose up"
