# Buffered Streaming Architecture - 20 Second Delay Solution

## 🎯 **Your Requested Approach**

You asked for a system that:
✅ **Stores frames locally** from RTSP/RTSPS streams  
✅ **Uses FFmpeg to create new streams** (.ts segments)  
✅ **Accepts 20-second delay** for stable playback  
✅ **Provides reliable video** without signal loss  

## 🏗️ **Architecture Overview**

```
RTSP Camera → Recording Process → Local TS Segments → Serving Process → HLS Stream → Browser
     ↓              ↓                    ↓                ↓              ↓
  Live Feed    Continuous Save    Buffered Files    20s Delayed    Stable Playback
```

### **Two-Stage Process:**

#### **Stage 1: Recording (Background)**
- **Continuous recording** of RTSP stream to local .ts segments
- **2-second segments** stored in temp directory
- **Rolling buffer** keeps last 60 seconds of video
- **High quality encoding** since it's not real-time

#### **Stage 2: Serving (20s Delayed)**
- **Reads recorded segments** with 20-second delay
- **Serves as HLS stream** to browsers
- **Copy mode** (no re-encoding) for efficiency
- **Multiple clients** can watch same buffered stream

## 🚀 **Benefits of This Approach**

### **Stability Benefits:**
✅ **No real-time pressure** - Recording can handle network hiccups  
✅ **Buffer absorbs issues** - 20s buffer smooths out problems  
✅ **Better quality** - Can use higher quality encoding  
✅ **Reliable playback** - Local files eliminate streaming issues  

### **Performance Benefits:**
✅ **Lower CPU usage** - Copy mode serving (no re-encoding)  
✅ **Better compression** - Time to optimize encoding  
✅ **Multiple viewers** - Same file served to all clients  
✅ **Bandwidth efficient** - Pre-encoded segments  

### **Operational Benefits:**
✅ **Perfect for surveillance** - 20s delay acceptable for monitoring  
✅ **Handles network issues** - Recording continues during problems  
✅ **Easy debugging** - Can inspect recorded segments  
✅ **Scalable** - Add viewers without affecting recording  

## 📁 **File Structure**

```
temp/recordings/
├── [stream-id-1]/
│   ├── segment_001.ts
│   ├── segment_002.ts
│   ├── segment_003.ts
│   └── concat.txt
└── [stream-id-2]/
    ├── segment_001.ts
    └── ...

public/buffered-streams/
├── [stream-id-1]/
│   ├── playlist.m3u8
│   ├── segment_001.ts
│   └── segment_002.ts
└── [stream-id-2]/
    └── ...
```

## ⚙️ **Technical Implementation**

### **Recording Process:**
```bash
ffmpeg -rtsp_transport tcp -i [RTSP_URL] \
  -c:v libx264 -preset fast -crf 23 \
  -c:a aac \
  -f segment -segment_time 2 \
  -segment_format mpegts \
  -segment_wrap 30 \
  temp/recordings/[stream-id]/segment_%03d.ts
```

### **Serving Process (20s Delayed):**
```bash
ffmpeg -f concat -safe 0 -i concat.txt \
  -c copy \
  -f hls -hls_time 2 -hls_list_size 10 \
  -hls_flags delete_segments+append_list \
  public/buffered-streams/[stream-id]/playlist.m3u8
```

### **Delay Management:**
- **Recording**: Saves segments immediately
- **Concat file**: Updated every 2 seconds, excludes last 10 segments (20s)
- **Serving**: Reads from delayed concat file
- **Result**: 20-second delay between live and playback

## 🎮 **How to Use**

### **1. Configure Streams**
```typescript
// In stream configuration
{
  "name": "Front Door",
  "url": "rtsps://camera.example.com/stream",
  "type": "buffered"  // ← Select buffered type
}
```

### **2. Stream Status Indicators**
- 🟡 **STARTING** - Initializing recording
- 🔵 **BUFFERING** - Building 20s buffer (progress bar)
- 🟢 **LIVE (-20s)** - Playing with 20s delay
- ⏸️ **PAUSED** - Tab inactive (saves resources)
- 🔴 **OFFLINE** - Error or stopped

### **3. Monitor Performance**
- Click "📊 Performance Monitor"
- See "Buffered Streams" section
- Monitor segment count and client connections

## 📊 **Performance Comparison**

| Approach | Latency | Stability | CPU Usage | Quality | Scalability |
|----------|---------|-----------|-----------|---------|-------------|
| **Real-time WebSocket** | 1-3s | Poor | High | Medium | Poor |
| **Real-time HLS** | 5-10s | Medium | Medium | Medium | Good |
| **Buffered (Your Request)** | 20s | Excellent | Low | High | Excellent |

## 🔧 **Configuration Options**

### **Buffer Duration (Default: 20s)**
```typescript
private readonly BUFFER_DURATION = 20; // seconds
```

### **Segment Duration (Default: 2s)**
```typescript
private readonly SEGMENT_DURATION = 2; // seconds
```

### **Rolling Buffer Size (Default: 30 segments = 60s)**
```typescript
'-segment_wrap', '30'  // Keep last 30 segments
```

## 🚨 **Troubleshooting**

### **Common Issues:**

1. **"No video after 20 seconds"**
   - Check FFmpeg is installed
   - Verify RTSP URL is accessible
   - Check temp directory permissions

2. **"Buffering stuck at X%"**
   - RTSP connection issue
   - Check network connectivity
   - Verify camera credentials

3. **"High disk usage"**
   - Segments are cleaned up automatically
   - Check cleanup interval settings
   - Monitor temp directory size

### **Debug Commands:**
```bash
# Check if segments are being created
ls -la temp/recordings/[stream-id]/

# Check if HLS playlist is being generated
ls -la public/buffered-streams/[stream-id]/

# Monitor FFmpeg processes
ps aux | grep ffmpeg
```

## 🎯 **Perfect For Your Use Case**

This buffered approach is **ideal** for surveillance because:

✅ **20s delay acceptable** - You're monitoring, not controlling  
✅ **Stable playback** - No stuttering or connection drops  
✅ **High quality** - Better encoding with time to optimize  
✅ **Multiple viewers** - Security team can all watch same feed  
✅ **Reliable recording** - Continues even if viewers disconnect  
✅ **Easy troubleshooting** - Can inspect recorded segments  

## 🚀 **Next Steps**

1. **Test the implementation** - Configure a stream with "buffered" type
2. **Monitor performance** - Use the performance monitor
3. **Adjust settings** - Tune buffer duration if needed
4. **Scale up** - Add more cameras with buffered streaming

This approach gives you the **stability and reliability** you need for professional surveillance monitoring!
