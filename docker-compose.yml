version: '3.8'

services:
  fish-tank-surveillance:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        # Build-time arguments (optional - will use defaults if not provided)
        WS_HOST_ARG: ${WS_HOST:-localhost}
        WS_PORT_ARG: ${WS_PORT:-8080}
        WS_PROTOCOL_ARG: ${WS_PROTOCOL:-ws}
        MAX_STREAMS_ARG: ${MAX_STREAMS:-10}
        DEFAULT_VIDEO_BITRATE_ARG: ${DEFAULT_VIDEO_BITRATE:-1000k}
        DEFAULT_AUDIO_BITRATE_ARG: ${DEFAULT_AUDIO_BITRATE:-128k}
        DEFAULT_FRAME_RATE_ARG: ${DEFAULT_FRAME_RATE:-30}
    container_name: fish-tank-surveillance
    restart: unless-stopped
    ports:
      - "${APP_PORT:-3000}:3000"
      # WebSocket port (configurable)
      - "${WS_PORT:-8080}:${WS_PORT:-8080}"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - NEXT_TELEMETRY_DISABLED=1
      - PORT=3000
      - HOSTNAME=0.0.0.0
      # WebSocket configuration (configurable via environment)
      - WS_HOST=${WS_HOST:-localhost}
      - WS_PORT=${WS_PORT:-8080}
      - WS_PROTOCOL=${WS_PROTOCOL:-ws}
      # SSL/TLS configuration (for WSS)
      - SSL_KEY_PATH=${SSL_KEY_PATH:-/app/ssl/key.pem}
      - SSL_CERT_PATH=${SSL_CERT_PATH:-/app/ssl/cert.pem}
      # Stream configuration (optional)
      - MAX_STREAMS=${MAX_STREAMS:-10}
      - DEFAULT_VIDEO_BITRATE=${DEFAULT_VIDEO_BITRATE:-1000k}
      - DEFAULT_AUDIO_BITRATE=${DEFAULT_AUDIO_BITRATE:-128k}
      - DEFAULT_FRAME_RATE=${DEFAULT_FRAME_RATE:-30}
      # Logging
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      # Persist stream configuration
      - ./public/config:/app/public/config:ro
      # Persist generated streams (optional)
      - streams_data:/app/public/streams
      # Persist logs
      - logs_data:/app/logs
      # SSL certificates (for WSS) - mount your certificates here
      - ${SSL_CERT_DIR:-./ssl}:/app/ssl:ro
    networks:
      - surveillance-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/config"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    # Security
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

volumes:
  streams_data:
    driver: local
  logs_data:
    driver: local

networks:
  surveillance-network:
    driver: bridge
