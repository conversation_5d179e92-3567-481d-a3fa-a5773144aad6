# Docker Environment Configuration Guide

This guide explains how to configure WebSocket host, port, and other settings using Docker environment variables and build arguments.

## 🎯 **Three Ways to Configure Environment**

### 1. **Runtime Environment Variables (Recommended)**
Set variables when running the container - most flexible approach.

### 2. **Build-time Arguments**
Set variables when building the image - baked into the image.

### 3. **Environment Files**
Use `.env` files for organized configuration management.

## 🚀 **Quick Start Examples**

### **Method 1: Runtime Environment Variables**

```bash
# Set environment variables and run
export WS_HOST=*************
export WS_PORT=8080
export MAX_STREAMS=20
docker-compose up -d
```

### **Method 2: Inline Environment Variables**

```bash
# Set variables inline
WS_HOST=surveillance.company.com WS_PORT=8080 docker-compose up -d
```

### **Method 3: Environment File**

```bash
# Create .env file
cat > .env << EOF
WS_HOST=surveillance.company.com
WS_PORT=8080
MAX_STREAMS=20
DEFAULT_VIDEO_BITRATE=1500k
EOF

# Run with environment file
docker-compose up -d
```

### **Method 4: Build-time Configuration**

```bash
# Build with custom arguments
docker-compose build --build-arg WS_HOST_ARG=surveillance.company.com --build-arg WS_PORT_ARG=8080

# Then run
docker-compose up -d
```

## 📋 **Available Configuration Options**

### **WebSocket Configuration**
| Variable | Default | Description |
|----------|---------|-------------|
| `WS_HOST` | `localhost` | WebSocket server hostname/IP |
| `WS_PORT` | `8080` | WebSocket server port |

### **Application Configuration**
| Variable | Default | Description |
|----------|---------|-------------|
| `APP_PORT` | `3000` | External port for web application |
| `NODE_ENV` | `production` | Node.js environment |
| `LOG_LEVEL` | `info` | Logging level (debug, info, warn, error) |

### **Stream Configuration**
| Variable | Default | Description |
|----------|---------|-------------|
| `MAX_STREAMS` | `10` | Maximum concurrent streams |
| `DEFAULT_VIDEO_BITRATE` | `1000k` | Default video bitrate |
| `DEFAULT_AUDIO_BITRATE` | `128k` | Default audio bitrate |
| `DEFAULT_FRAME_RATE` | `30` | Default frame rate |

## 🌍 **Environment-Specific Examples**

### **Local Development**
```bash
# .env.local
NODE_ENV=development
WS_HOST=localhost
WS_PORT=8080
APP_PORT=3000
LOG_LEVEL=debug
MAX_STREAMS=5
```

```bash
docker-compose --env-file .env.local up -d
```

### **Staging Environment**
```bash
# .env.staging
NODE_ENV=production
WS_HOST=staging.surveillance.company.com
WS_PORT=8080
APP_PORT=3000
LOG_LEVEL=info
MAX_STREAMS=15
DEFAULT_VIDEO_BITRATE=1200k
```

```bash
docker-compose --env-file .env.staging up -d
```

### **Production Environment**
```bash
# .env.production
NODE_ENV=production
WS_HOST=surveillance.company.com
WS_PORT=8080
APP_PORT=80
LOG_LEVEL=warn
MAX_STREAMS=25
DEFAULT_VIDEO_BITRATE=1500k
DEFAULT_AUDIO_BITRATE=192k
```

```bash
docker-compose --env-file .env.production -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 🔧 **Advanced Configuration**

### **Custom Ports**
```bash
# Use different ports
export WS_HOST=surveillance.company.com
export WS_PORT=9090
export APP_PORT=8080
docker-compose up -d
```

### **Multiple Environments**
```bash
# Development
docker-compose --env-file .env.dev up -d

# Staging  
docker-compose --env-file .env.staging up -d

# Production
docker-compose --env-file .env.prod -f docker-compose.prod.yml up -d
```

### **Override Specific Variables**
```bash
# Use .env file but override specific variables
docker-compose --env-file .env.production up -d
# Then override at runtime:
docker-compose exec fish-tank-surveillance env WS_HOST=new-host.com
```

## 🐳 **Docker Commands Reference**

### **Build with Arguments**
```bash
# Build with custom WebSocket configuration
docker build \
  --build-arg WS_HOST_ARG=surveillance.company.com \
  --build-arg WS_PORT_ARG=8080 \
  --build-arg MAX_STREAMS_ARG=20 \
  -t fish-tank-surveillance .
```

### **Run with Environment Variables**
```bash
# Run container directly with environment variables
docker run -d \
  -p 3000:3000 \
  -p 8080:8080 \
  -e WS_HOST=surveillance.company.com \
  -e WS_PORT=8080 \
  -e MAX_STREAMS=20 \
  fish-tank-surveillance
```

### **Inspect Configuration**
```bash
# Check environment variables in running container
docker exec fish-tank-surveillance env | grep WS_

# Check build arguments used
docker inspect fish-tank-surveillance | grep -A 10 "Config"
```

## 🔍 **Troubleshooting**

### **Check Current Configuration**
```bash
# View effective environment variables
docker-compose config

# Check running container environment
docker exec fish-tank-surveillance printenv | grep -E "(WS_|MAX_|DEFAULT_)"
```

### **Test WebSocket Connection**
```bash
# Test WebSocket connectivity with current configuration
WS_HOST=$(docker exec fish-tank-surveillance printenv WS_HOST)
WS_PORT=$(docker exec fish-tank-surveillance printenv WS_PORT)
echo "Testing WebSocket at: ws://$WS_HOST:$WS_PORT"
```

### **Common Issues**

1. **Environment variables not taking effect**:
   - Rebuild the container: `docker-compose up -d --build`
   - Check variable precedence: runtime > build-time > defaults

2. **WebSocket connection fails**:
   - Verify `WS_HOST` is accessible from client
   - Check `WS_PORT` is exposed and not blocked by firewall

3. **Port conflicts**:
   - Change `APP_PORT` or `WS_PORT` to avoid conflicts
   - Use `docker-compose ps` to check port mappings

## 📝 **Best Practices**

1. **Use .env files** for organized configuration
2. **Set WS_HOST** to your actual domain/IP in production
3. **Use build arguments** for values that rarely change
4. **Use runtime environment** for values that change between deployments
5. **Document your configuration** for team members
6. **Test configuration** before production deployment

## 🎯 **Quick Reference**

```bash
# Most common production setup
cat > .env << EOF
WS_HOST=your-domain.com
WS_PORT=8080
MAX_STREAMS=20
LOG_LEVEL=warn
EOF

docker-compose up -d
```
