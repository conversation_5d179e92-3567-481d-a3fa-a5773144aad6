# Fish Tank Surveillance Dashboard - Packaging Guide

This guide explains how to package the Fish Tank Surveillance Dashboard for distribution and deployment.

## 📦 Available Packaging Scripts

### Unix/Linux/macOS
- **`package.sh`** - Main packaging script with full features
- **Usage**: `./package.sh [version] [--include-build]`

### Windows
- **`package.ps1`** - PowerShell script with full features
- **`package.bat`** - Command Prompt batch file (basic)
- **Usage**: 
  - PowerShell: `.\package.ps1 [version] [-IncludeBuild]`
  - Batch: `package.bat [version]`

## 🚀 Quick Start

### Linux/macOS
```bash
# Make script executable
chmod +x package.sh

# Create package
./package.sh v1.0.0

# Create package with build files
./package.sh v1.0.0 --include-build
```

### Windows PowerShell
```powershell
# Create package
.\package.ps1 v1.0.0

# Create package with build files
.\package.ps1 v1.0.0 -IncludeBuild
```

### Windows Command Prompt
```cmd
# Create package
package.bat v1.0.0
```

## 📋 Package Contents

### Always Included
- **Source Code**: `src/` directory with all React components and API routes
- **Public Assets**: `public/` directory including configuration and images
- **Configuration**: Next.js, TypeScript, Tailwind, and ESLint configs
- **Docker Setup**: Dockerfile, docker-compose files, and build scripts
- **Documentation**: README, setup guides, and version information
- **Installation Scripts**: Automated setup for different platforms

### Optional (with flags)
- **Build Files**: Pre-compiled Next.js application (`.next/` directory)
- **Dependencies**: Installed node_modules (not recommended for distribution)

## 📁 Generated Archive Structure

```
fish-tank-surveillance_v1.0.0_20241225_143022.tar.gz
├── src/                          # Source code
│   ├── app/                      # Next.js app directory
│   ├── components/               # React components
│   └── types/                    # TypeScript definitions
├── public/                       # Static assets
│   ├── config/                   # Stream configuration
│   ├── images/                   # Logo and images
│   └── streams/                  # Generated stream files (empty)
├── package.json                  # Dependencies and scripts
├── next.config.ts               # Next.js configuration
├── tsconfig.json                # TypeScript configuration
├── Dockerfile                   # Docker container setup
├── docker-compose.yml           # Docker Compose configuration
├── install.sh / install.bat     # Installation scripts
├── VERSION_INFO.txt             # Build and version information
└── README.md                    # Project documentation
```

## 🛠️ Installation from Package

### Automatic Installation

#### Linux/macOS
```bash
# Extract archive
tar -xzf fish-tank-surveillance_v1.0.0_*.tar.gz
cd fish-tank-surveillance_v1.0.0_*

# Run installation script
./install.sh
```

#### Windows
```cmd
# Extract archive
tar -xzf fish-tank-surveillance_v1.0.0_*.tar.gz
cd fish-tank-surveillance_v1.0.0_*

# Run installation script
install.bat
```

### Manual Installation
```bash
# Extract archive
tar -xzf fish-tank-surveillance_v1.0.0_*.tar.gz
cd fish-tank-surveillance_v1.0.0_*

# Install dependencies
npm install

# Configure streams
# Edit public/config/streams.json

# Build application
npm run build

# Start application
npm start
```

## 🐳 Docker Deployment from Package

```bash
# Extract archive
tar -xzf fish-tank-surveillance_v1.0.0_*.tar.gz
cd fish-tank-surveillance_v1.0.0_*

# Build and run with Docker
./docker-build.sh

# Or use Docker Compose directly
docker-compose up -d
```

## ⚙️ Configuration

### Package Configuration
Edit `package-config.json` to customize:
- Included/excluded files and directories
- Project metadata
- Deployment requirements
- Script locations

### Stream Configuration
After installation, configure your camera streams:
```bash
# Edit the configuration file
nano public/config/streams.json
```

## 📊 Package Information

Each package includes a `VERSION_INFO.txt` file with:
- Version and build information
- Git commit and branch (if available)
- Build environment details
- Installation instructions
- Package contents summary

## 🔧 Troubleshooting

### Common Issues

1. **tar command not found**
   - **Linux**: `sudo apt install tar` or `sudo yum install tar`
   - **Windows**: Use Windows 10 1903+ or install tar separately

2. **Permission denied on scripts**
   - **Linux/macOS**: `chmod +x package.sh`
   - **Windows**: Run PowerShell as Administrator

3. **Large package size**
   - Don't include build files unless necessary
   - Exclude node_modules from packages
   - Use `.dockerignore` patterns

### Package Validation
```bash
# Check archive contents
tar -tzf package_name.tar.gz

# Verify archive integrity
tar -tzf package_name.tar.gz > /dev/null && echo "Archive OK"
```

## 📈 Best Practices

1. **Version Naming**: Use semantic versioning (v1.0.0, v1.1.0, etc.)
2. **Clean Build**: Run `npm run build` before packaging with build files
3. **Test Packages**: Always test installation from packages before distribution
4. **Documentation**: Keep README and setup guides up to date
5. **Size Optimization**: Exclude unnecessary files to reduce package size

## 🎯 Use Cases

- **Development Distribution**: Share code with team members
- **Production Deployment**: Package for server deployment
- **Client Delivery**: Provide complete solution to clients
- **Backup/Archive**: Create project snapshots
- **CI/CD Integration**: Automated packaging in build pipelines
