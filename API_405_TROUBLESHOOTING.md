# API 405 Error Troubleshooting Guide

## Current Status
✅ Build successful - All API routes are included in the build
✅ Disabled standalone mode to fix deployment issues
✅ Added comprehensive logging and debugging
✅ Created multiple test endpoints

## Step-by-Step Troubleshooting

### 1. Test Basic API Functionality First
Test these endpoints in order to isolate the issue:

```bash
# 1. Simple test endpoint
curl https://surv.incubasestudio.com/api/test

# 2. Health check endpoint  
curl https://surv.incubasestudio.com/api/health

# 3. Alternative stream endpoint (simplified)
curl "https://surv.incubasestudio.com/api/stream-alt?url=test&name=test"

# 4. Original stream endpoint
curl "https://surv.incubasestudio.com/api/stream?url=test&name=test"
```

### 2. Expected Responses

**✅ If working correctly:**
```json
{
  "status": "API routes working",
  "timestamp": "2025-01-04T...",
  "environment": "production",
  "method": "GET"
}
```

**❌ If still 405 error:**
- The issue is with your hosting provider/server configuration
- API routes are not being handled correctly in production

### 3. Browser Testing
Open browser dev tools and:

1. **Network Tab**: Check the actual HTTP status and response
2. **Console**: Look for JavaScript errors
3. **Hard Refresh**: Ctrl+F5 to bypass cache

### 4. Common Causes & Solutions

#### A. Caching Issues (Most Common)
```bash
# Clear all caches
- Browser cache (Ctrl+F5)
- CDN cache (Cloudflare, etc.)
- Server cache
- Application cache
```

#### B. Server Configuration Issues
Your hosting provider might not support Next.js API routes properly.

**For Nginx:**
```nginx
location /api/ {
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
}
```

**For Apache:**
```apache
ProxyPass /api/ http://localhost:3000/api/
ProxyPassReverse /api/ http://localhost:3000/api/
```

#### C. Deployment Platform Issues
Different platforms handle Next.js differently:

**Vercel**: Should work automatically
**Netlify**: Might need `_redirects` file
**Docker**: Use updated Dockerfile (provided)
**VPS/Dedicated**: Check web server config

### 5. Deployment Fixes Applied

#### Updated Dockerfile
```dockerfile
# Changed from standalone to standard Next.js
COPY --from=deps --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
CMD ["npm", "start"]  # Instead of node server.js
```

#### Updated Next.js Config
```typescript
// Disabled standalone mode
// output: 'standalone',
```

### 6. Debug Information

The build logs show all API routes are loaded:
- `/api/stream` ✅
- `/api/test` ✅ 
- `/api/stream-alt` ✅
- `/api/health` ✅

### 7. If Still Not Working

#### Option A: Contact Hosting Provider
Ask them about:
- Next.js API route support
- Proxy configuration for `/api/*` paths
- Node.js application deployment

#### Option B: Alternative Deployment
Try deploying to:
- Vercel (native Next.js support)
- Railway
- Render
- DigitalOcean App Platform

#### Option C: Static Export (No API)
If API routes can't work, switch to static export:
```typescript
// next.config.ts
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: { unoptimized: true }
};
```

### 8. Immediate Actions

1. **Deploy the updated build** with these fixes
2. **Test `/api/test` first** - if this fails, it's a server config issue
3. **Check server logs** for any error messages
4. **Contact hosting provider** if basic API routes don't work

### 9. Verification Commands

After deployment:
```bash
# Test all endpoints
curl -v https://surv.incubasestudio.com/api/test
curl -v https://surv.incubasestudio.com/api/health  
curl -v https://surv.incubasestudio.com/api/stream-alt?url=test&name=test
curl -v https://surv.incubasestudio.com/api/stream?url=test&name=test

# Check response headers
curl -I https://surv.incubasestudio.com/api/test
```

The key is to test the simple endpoints first to determine if it's a general API routing issue or specific to the stream API.
