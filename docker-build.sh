#!/bin/bash

# Fish Tank Surveillance Dashboard - Docker Build Script
# Usage: ./docker-build.sh [dev|prod]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="fish-tank-surveillance"
VERSION=${VERSION:-"latest"}
ENVIRONMENT=${1:-"dev"}

echo -e "${BLUE}🐟 Fish Tank Surveillance Dashboard - Docker Build${NC}"
echo -e "${BLUE}=================================================${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running"

# Build the image
echo -e "\n${BLUE}Building Docker image...${NC}"
if docker build -t "${IMAGE_NAME}:${VERSION}" -t "${IMAGE_NAME}:latest" .; then
    print_status "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Show image size
IMAGE_SIZE=$(docker images "${IMAGE_NAME}:latest" --format "table {{.Size}}" | tail -n 1)
print_status "Image size: ${IMAGE_SIZE}"

# Environment-specific actions
case $ENVIRONMENT in
    "dev")
        echo -e "\n${BLUE}Development Environment${NC}"
        print_status "Starting development container..."
        docker-compose up -d
        print_status "Container started. Access at http://localhost:3000"
        ;;
    "prod")
        echo -e "\n${BLUE}Production Environment${NC}"
        print_status "Use the following commands to deploy:"
        echo -e "  ${YELLOW}docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d${NC}"
        ;;
    *)
        print_warning "Unknown environment: $ENVIRONMENT"
        print_status "Available environments: dev, prod"
        ;;
esac

# Show useful commands
echo -e "\n${BLUE}Useful Commands:${NC}"
echo -e "  View logs:     ${YELLOW}docker-compose logs -f${NC}"
echo -e "  Stop:          ${YELLOW}docker-compose down${NC}"
echo -e "  Restart:       ${YELLOW}docker-compose restart${NC}"
echo -e "  Shell access:  ${YELLOW}docker-compose exec fish-tank-surveillance sh${NC}"
echo -e "  Remove image:  ${YELLOW}docker rmi ${IMAGE_NAME}:latest${NC}"

print_status "Build completed successfully!"
