# Multi-stage build for optimized production image
FROM node:20-alpine AS base
LABEL authors="Nick Chan"
LABEL description="Fish Tank Surveillance Dashboard"

# Install system dependencies
RUN apk add --no-cache \
    ffmpeg \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create app user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

WORKDIR /app

# Dependencies stage
FROM base AS deps
# Copy package files
COPY package*.json ./
# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Build stage
FROM base AS builder
WORKDIR /app
COPY package*.json ./
# Install all dependencies (including dev)
RUN npm ci
# Copy source code
COPY . .
# Build the application
RUN npm run build

# Production stage
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Copy built application from standalone build
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Create directories for streams and logs with proper permissions
RUN mkdir -p /app/public/streams /app/logs /tmp && \
    chown -R nextjs:nodejs /app /tmp

USER nextjs

# Expose ports
EXPOSE 3000
EXPOSE 8080

# Build-time arguments (optional - for build-time configuration)
ARG WS_HOST_ARG="localhost"
ARG WS_PORT_ARG="8080"
ARG WS_PROTOCOL_ARG="ws"
ARG MAX_STREAMS_ARG="10"
ARG DEFAULT_VIDEO_BITRATE_ARG="1000k"
ARG DEFAULT_AUDIO_BITRATE_ARG="128k"
ARG DEFAULT_FRAME_RATE_ARG="30"

# Application configuration
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# WebSocket configuration (set from ARG, can still be overridden at runtime)
ENV WS_HOST=${WS_HOST_ARG}
ENV WS_PORT=${WS_PORT_ARG}
ENV WS_PROTOCOL=${WS_PROTOCOL_ARG}

# Stream configuration (set from ARG, can still be overridden at runtime)
ENV MAX_STREAMS=${MAX_STREAMS_ARG}
ENV DEFAULT_VIDEO_BITRATE=${DEFAULT_VIDEO_BITRATE_ARG}
ENV DEFAULT_AUDIO_BITRATE=${DEFAULT_AUDIO_BITRATE_ARG}
ENV DEFAULT_FRAME_RATE=${DEFAULT_FRAME_RATE_ARG}

# Health check using the config API endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/config || exit 1

# Use dumb-init to handle signals properly and start the Next.js server
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server.js"]
