# Production API 405 Error Fix

## Issue
Production deployment returns 405 "Method Not Allowed" for `/api/stream` and other API routes.

## Root Cause
The issue is likely one of:
1. **Standalone output mode** causing API routes to not be deployed correctly
2. **Server configuration** not handling API routes properly
3. **Missing dependencies** in production environment
4. **Caching issues** serving old version

## Fixes Applied

### 1. Disabled Standalone Output
```typescript
// next.config.ts - Commented out standalone mode
// output: 'standalone',
```

### 2. Added CORS Headers
```typescript
// Added OPTIONS handler to all API routes
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
```

### 3. Added Diagnostic Routes
- `/api/test` - Simple test endpoint
- `/api/health` - Health check with environment info

## Testing Steps

### Step 1: Test Basic API Functionality
```bash
# Test if API routes work at all
curl https://surv.incubasestudio.com/api/test
curl https://surv.incubasestudio.com/api/health
```

Expected response:
```json
{
  "status": "API routes working",
  "timestamp": "2025-01-04T...",
  "environment": "production"
}
```

### Step 2: Test Stream API
```bash
# Test stream API with proper parameters
curl "https://surv.incubasestudio.com/api/stream?url=rtsp://test&name=Test"
```

### Step 3: Check Browser Console
Open browser dev tools and check:
1. Network tab for actual HTTP status codes
2. Console for JavaScript errors
3. Response headers

## Deployment Options

### Option A: Standard Next.js Deployment
```bash
npm run build
npm start
```

### Option B: Docker with Standard Mode
```dockerfile
# Use standard Next.js mode instead of standalone
# Remove: output: 'standalone' from next.config.ts
```

### Option C: Static Export (if API not needed)
```typescript
// next.config.ts
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: { unoptimized: true }
};
```

## Server Configuration

### Nginx Configuration
```nginx
location /api/ {
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
}
```

### Apache Configuration
```apache
ProxyPass /api/ http://localhost:3000/api/
ProxyPassReverse /api/ http://localhost:3000/api/
ProxyPreserveHost On
```

## Environment Variables
Make sure these are set in production:
```bash
NODE_ENV=production
PORT=3000
```

## Cache Clearing
If using CDN or caching:
```bash
# Clear Cloudflare cache
# Clear server cache
# Force browser refresh (Ctrl+F5)
```

## Verification Commands

After deployment, run these tests:

```bash
# 1. Test health endpoint
curl -v https://surv.incubasestudio.com/api/health

# 2. Test with OPTIONS
curl -X OPTIONS https://surv.incubasestudio.com/api/stream

# 3. Test stream endpoint
curl "https://surv.incubasestudio.com/api/stream?url=rtsp://test&name=Test"

# 4. Check response headers
curl -I https://surv.incubasestudio.com/api/test
```

## Expected Results
- Status: 200 OK (not 405)
- Content-Type: application/json
- CORS headers present
- Valid JSON response

## If Still Not Working
1. Check server logs for errors
2. Verify Node.js version compatibility
3. Check if FFmpeg is installed in production
4. Verify all dependencies are installed
5. Try deploying without standalone mode
6. Contact hosting provider about API route support
