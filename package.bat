@echo off
REM Fish Tank Surveillance Dashboard - Packaging Script (Windows Batch)
REM Usage: package.bat [version]

setlocal enabledelayedexpansion

set PROJECT_NAME=fish-tank-surveillance
set VERSION=%1
if "%VERSION%"=="" set VERSION=latest

REM Get timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set TIMESTAMP=%YYYY%%MM%%DD%_%HH%%Min%%Sec%

set ARCHIVE_NAME=%PROJECT_NAME%_%VERSION%_%TIMESTAMP%.tar.gz
set TEMP_DIR=package_temp

echo 🐟 Fish Tank Surveillance Dashboard - Packaging Script
echo ====================================================

REM Check if tar is available
where tar >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ tar command not found. Please install tar or use Windows 10 1903+.
    pause
    exit /b 1
)
echo ✅ tar command found

REM Clean up any existing temp directory
if exist "%TEMP_DIR%" (
    echo ℹ Cleaning up existing temp directory...
    rmdir /s /q "%TEMP_DIR%"
)

REM Create temp directory
mkdir "%TEMP_DIR%"
echo ✅ Created temporary directory: %TEMP_DIR%

echo ℹ Copying project files...

REM Copy essential directories
if exist "src" (
    xcopy /e /i /q "src" "%TEMP_DIR%\src"
    echo ✅ Copied src/
) else (
    echo ⚠ src/ not found
)

if exist "public" (
    xcopy /e /i /q "public" "%TEMP_DIR%\public"
    echo ✅ Copied public/
) else (
    echo ⚠ public/ not found
)

REM Copy essential files
set FILES=package.json package-lock.json next.config.ts tsconfig.json tailwind.config.ts postcss.config.mjs eslint.config.mjs

for %%f in (%FILES%) do (
    if exist "%%f" (
        copy "%%f" "%TEMP_DIR%\" >nul
        echo ✅ Copied %%f
    ) else (
        echo ⚠ %%f not found
    )
)

REM Copy Docker files
set DOCKER_FILES=Dockerfile docker-compose.yml docker-compose.prod.yml docker-build.sh .dockerignore

for %%f in (%DOCKER_FILES%) do (
    if exist "%%f" (
        copy "%%f" "%TEMP_DIR%\" >nul
        echo ✅ Copied %%f
    ) else (
        echo ⚠ %%f not found
    )
)

REM Copy documentation
set DOC_FILES=README.md FFMPEG_SETUP.md .gitignore

for %%f in (%DOC_FILES%) do (
    if exist "%%f" (
        copy "%%f" "%TEMP_DIR%\" >nul
        echo ✅ Copied %%f
    ) else (
        echo ⚠ %%f not found
    )
)

REM Create version info file
echo Fish Tank Surveillance Dashboard > "%TEMP_DIR%\VERSION_INFO.txt"
echo ================================ >> "%TEMP_DIR%\VERSION_INFO.txt"
echo. >> "%TEMP_DIR%\VERSION_INFO.txt"
echo Version: %VERSION% >> "%TEMP_DIR%\VERSION_INFO.txt"
echo Build Date: %date% %time% >> "%TEMP_DIR%\VERSION_INFO.txt"
echo Build Host: %COMPUTERNAME% >> "%TEMP_DIR%\VERSION_INFO.txt"
echo Build User: %USERNAME% >> "%TEMP_DIR%\VERSION_INFO.txt"
echo. >> "%TEMP_DIR%\VERSION_INFO.txt"
echo Package Contents: >> "%TEMP_DIR%\VERSION_INFO.txt"
echo - Source code (src/) >> "%TEMP_DIR%\VERSION_INFO.txt"
echo - Public assets (public/) >> "%TEMP_DIR%\VERSION_INFO.txt"
echo - Configuration files >> "%TEMP_DIR%\VERSION_INFO.txt"
echo - Docker setup >> "%TEMP_DIR%\VERSION_INFO.txt"
echo - Documentation >> "%TEMP_DIR%\VERSION_INFO.txt"
echo. >> "%TEMP_DIR%\VERSION_INFO.txt"
echo Installation: >> "%TEMP_DIR%\VERSION_INFO.txt"
echo 1. Extract the archive >> "%TEMP_DIR%\VERSION_INFO.txt"
echo 2. Run: npm install >> "%TEMP_DIR%\VERSION_INFO.txt"
echo 3. Configure streams in public/config/streams.json >> "%TEMP_DIR%\VERSION_INFO.txt"
echo 4. Build: npm run build >> "%TEMP_DIR%\VERSION_INFO.txt"
echo 5. Start: npm start >> "%TEMP_DIR%\VERSION_INFO.txt"

echo ✅ Version info created

REM Create the tar archive
echo ℹ Creating tar archive: %ARCHIVE_NAME%
cd "%TEMP_DIR%"
tar -czf "..\%ARCHIVE_NAME%" *
cd ..

REM Get archive size
for %%A in ("%ARCHIVE_NAME%") do set ARCHIVE_SIZE=%%~zA
set /a ARCHIVE_SIZE_MB=%ARCHIVE_SIZE%/1024/1024
echo ✅ Archive created: %ARCHIVE_NAME% (%ARCHIVE_SIZE_MB% MB)

REM Clean up temp directory
rmdir /s /q "%TEMP_DIR%"
echo ✅ Cleaned up temporary files

echo.
echo 🎉 Package created successfully!
echo Archive: %ARCHIVE_NAME%
echo Size: %ARCHIVE_SIZE_MB% MB
echo.
echo Usage:
echo   Extract: tar -xzf %ARCHIVE_NAME%
echo   Install: cd extracted_folder ^&^& install.bat
echo   Docker:  cd extracted_folder ^&^& docker-compose up

pause
