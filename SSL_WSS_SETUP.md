# SSL/WSS Configuration Guide

This guide explains how to configure SSL/TLS for secure WebSocket connections (WSS) in production environments with HTTPS.

## 🔒 **Why W<PERSON> is Required for HTTPS**

When your application runs on HTTPS, browsers require WebSocket connections to use WSS (WebSocket Secure) due to mixed content security policies. HTTP sites can use WS, but HTTPS sites must use WSS.

## 🎯 **Quick Configuration**

### **Development (HTTP + WS)**
```bash
# .env.local
WS_PROTOCOL=ws
WS_HOST=localhost
WS_PORT=8080
```

### **Production (HTTPS + WSS)**
```bash
# .env.https
WS_PROTOCOL=wss
WS_HOST=surveillance.yourdomain.com
WS_PORT=443
SSL_KEY_PATH=/app/ssl/privkey.pem
SSL_CERT_PATH=/app/ssl/fullchain.pem
```

## 🚀 **Deployment Scenarios**

### **Scenario 1: Direct SSL in Container**

```bash
# 1. Prepare SSL certificates
mkdir -p ./ssl
cp /path/to/your/privkey.pem ./ssl/
cp /path/to/your/fullchain.pem ./ssl/

# 2. Configure environment
cat > .env << EOF
WS_PROTOCOL=wss
WS_HOST=surveillance.yourdomain.com
WS_PORT=443
SSL_CERT_DIR=./ssl
EOF

# 3. Deploy
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### **Scenario 2: Reverse Proxy (Recommended)**

Use Nginx/Apache to handle SSL termination and proxy to the container:

```bash
# Container uses WS (internal)
cat > .env << EOF
WS_PROTOCOL=ws
WS_HOST=localhost
WS_PORT=8080
EOF

# Nginx handles WSS (external)
# See Nginx configuration below
```

### **Scenario 3: Let's Encrypt with Certbot**

```bash
# 1. Generate certificates
sudo certbot certonly --standalone -d surveillance.yourdomain.com

# 2. Configure environment
cat > .env << EOF
WS_PROTOCOL=wss
WS_HOST=surveillance.yourdomain.com
WS_PORT=443
SSL_CERT_DIR=/etc/letsencrypt/live/surveillance.yourdomain.com
EOF

# 3. Deploy with certificate volume
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 🌐 **Reverse Proxy Configuration (Recommended)**

### **Nginx Configuration**

```nginx
# /etc/nginx/sites-available/surveillance
server {
    listen 80;
    server_name surveillance.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name surveillance.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/surveillance.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/surveillance.yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Main application
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket proxy (WSS)
    location /ws {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }
}
```

### **Apache Configuration**

```apache
<VirtualHost *:80>
    ServerName surveillance.yourdomain.com
    Redirect permanent / https://surveillance.yourdomain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName surveillance.yourdomain.com
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /etc/letsencrypt/live/surveillance.yourdomain.com/fullchain.pem
    SSLCertificateKeyFile /etc/letsencrypt/live/surveillance.yourdomain.com/privkey.pem
    
    # Main application
    ProxyPass / http://localhost:3000/
    ProxyPassReverse / http://localhost:3000/
    
    # WebSocket proxy (WSS)
    ProxyPass /ws ws://localhost:8080/ws
    ProxyPassReverse /ws ws://localhost:8080/ws
    
    # Headers
    ProxyPreserveHost On
    ProxyAddHeaders On
</VirtualHost>
```

## 🔧 **Environment Configuration Examples**

### **Development Environment**
```bash
# .env.local
NODE_ENV=development
WS_PROTOCOL=ws
WS_HOST=localhost
WS_PORT=8080
APP_PORT=3000
```

### **Staging Environment**
```bash
# .env.staging
NODE_ENV=production
WS_PROTOCOL=wss
WS_HOST=staging.surveillance.company.com
WS_PORT=443
APP_PORT=443
SSL_CERT_DIR=/etc/ssl/staging
```

### **Production Environment**
```bash
# .env.production
NODE_ENV=production
WS_PROTOCOL=wss
WS_HOST=surveillance.company.com
WS_PORT=443
APP_PORT=443
SSL_CERT_DIR=/etc/letsencrypt/live/surveillance.company.com
```

## 📋 **SSL Certificate Setup**

### **Option 1: Let's Encrypt (Free)**

```bash
# Install certbot
sudo apt install certbot

# Generate certificate
sudo certbot certonly --standalone -d surveillance.yourdomain.com

# Certificates will be in:
# /etc/letsencrypt/live/surveillance.yourdomain.com/
```

### **Option 2: Self-Signed (Development)**

```bash
# Create SSL directory
mkdir -p ./ssl

# Generate self-signed certificate
openssl req -x509 -newkey rsa:4096 -keyout ./ssl/key.pem -out ./ssl/cert.pem -days 365 -nodes \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=surveillance.localhost"

# Use in development
export SSL_CERT_DIR=./ssl
export WS_PROTOCOL=wss
```

### **Option 3: Commercial Certificate**

```bash
# Place your certificates in SSL directory
mkdir -p ./ssl
cp your-private-key.pem ./ssl/key.pem
cp your-certificate.pem ./ssl/cert.pem

# Configure environment
export SSL_CERT_DIR=./ssl
export WS_PROTOCOL=wss
```

## 🚀 **Deployment Commands**

### **HTTP Development**
```bash
docker-compose --env-file .env.local up -d
```

### **HTTPS Production with Direct SSL**
```bash
# Ensure certificates are in ./ssl/
docker-compose --env-file .env.https -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### **HTTPS Production with Reverse Proxy**
```bash
# Container uses WS internally, Nginx handles WSS
docker-compose --env-file .env.local up -d
# Configure Nginx as shown above
```

## 🔍 **Testing SSL/WSS Configuration**

### **Test WebSocket Connection**
```bash
# Test WSS connection
wscat -c wss://surveillance.yourdomain.com/ws?streamId=test

# Test WS connection (development)
wscat -c ws://localhost:8080/ws?streamId=test
```

### **Check SSL Certificate**
```bash
# Check certificate validity
openssl s_client -connect surveillance.yourdomain.com:443 -servername surveillance.yourdomain.com

# Check certificate expiration
echo | openssl s_client -connect surveillance.yourdomain.com:443 2>/dev/null | openssl x509 -noout -dates
```

### **Browser Testing**
```javascript
// In browser console
const ws = new WebSocket('wss://surveillance.yourdomain.com/ws?streamId=test');
ws.onopen = () => console.log('WSS connection opened');
ws.onerror = (error) => console.error('WSS connection error:', error);
```

## 🛠️ **Troubleshooting**

### **Common Issues**

1. **Mixed Content Error**
   - Ensure WS_PROTOCOL=wss for HTTPS sites
   - Check browser console for mixed content warnings

2. **Certificate Not Found**
   - Verify SSL_CERT_DIR path is correct
   - Check file permissions (readable by container user)

3. **WSS Connection Refused**
   - Verify port 443 is open and accessible
   - Check if reverse proxy is properly configured

4. **Certificate Expired**
   - Renew Let's Encrypt certificates: `sudo certbot renew`
   - Update certificate files in SSL directory

### **Debug Commands**
```bash
# Check container environment
docker exec fish-tank-surveillance printenv | grep -E "(WS_|SSL_)"

# Check certificate files in container
docker exec fish-tank-surveillance ls -la /app/ssl/

# Check WebSocket server logs
docker logs fish-tank-surveillance | grep -i websocket
```

## 🔐 **Security Best Practices**

1. **Use Strong SSL Configuration**
   - TLS 1.2+ only
   - Strong cipher suites
   - HSTS headers

2. **Certificate Management**
   - Regular certificate renewal
   - Secure private key storage
   - Certificate monitoring

3. **Firewall Configuration**
   - Only expose necessary ports (80, 443)
   - Block direct access to internal ports (3000, 8080)

4. **Regular Updates**
   - Keep SSL libraries updated
   - Monitor security advisories
   - Regular security audits
