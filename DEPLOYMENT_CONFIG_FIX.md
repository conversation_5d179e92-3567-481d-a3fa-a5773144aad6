# Deployment Configuration Fix

## Issue
After deployment, the `/api/config` endpoint returns a 405 "Method Not Allowed" error because the configuration file cannot be found in the production environment.

## Root Cause
In Next.js production builds with `output: 'standalone'`, the file system structure is different, and the `public/config/streams.json` file may not be accessible at the expected path.

## Solution
The `/api/config` route has been enhanced with multiple fallback mechanisms:

### 1. Multiple Path Resolution
The API now tries multiple possible paths for the configuration file:
- `{cwd}/public/config/streams.json` (development)
- `{cwd}/.next/static/config/streams.json` (build output)
- `{cwd}/config/streams.json` (alternative location)
- `/app/public/config/streams.json` (Docker container)

### 2. Environment Variable Fallback
If no configuration file is found, the API checks for a `STREAMS_CONFIG` environment variable containing the JSON configuration.

### 3. Default Configuration
If neither file nor environment variable is available, the API returns a default configuration with 4 sample streams.

## Deployment Options

### Option 1: Environment Variable (Recommended)
Set the `STREAMS_CONFIG` environment variable with your complete configuration:

```bash
export STREAMS_CONFIG='{"streams":[{"id":"camera-1","name":"Camera 1","url":"rtsp://...","type":"websocket","enabled":true}],"settings":{"defaultLayout":"2x2","autoStartFullscreen":false,"showManualControls":true,"refreshInterval":30000}}'
```

### Option 2: Docker Volume Mount
Mount the configuration file into the container:

```yaml
# docker-compose.yml
services:
  surveillance:
    volumes:
      - ./config:/app/public/config
```

### Option 3: Copy During Build
Add the configuration file to your Docker image during build:

```dockerfile
# In Dockerfile
COPY public/config/streams.json /app/public/config/streams.json
```

## Testing the Fix

1. **Local Testing**: The API should work normally with the existing `public/config/streams.json` file.

2. **Production Testing**: 
   - Deploy without the config file - should return default configuration
   - Set `STREAMS_CONFIG` environment variable - should return environment configuration
   - Mount config file - should return file configuration

## API Response Format
The API now includes a `source` field indicating where the configuration came from:
- `"source": "file"` - Loaded from configuration file
- `"source": "environment"` - Loaded from environment variable  
- `"source": "default"` - Using default fallback configuration

## Debugging
The API now logs detailed information about:
- Paths attempted for configuration file
- Current working directory
- Whether environment variable is available
- Source of configuration used

Check your deployment logs for these debug messages to troubleshoot configuration loading issues.
