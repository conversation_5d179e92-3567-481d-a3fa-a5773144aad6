# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next
out
build

# Environment files
.env*.local
.env
.env.development
.env.production

# Testing
coverage
.nyc_output

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
*.md

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Temporary files
tmp
temp
.tmp

# Video streaming files (will be generated at runtime)
public/streams/*

# Development files
.eslintrc.json
.prettierrc
.editorconfig

# TypeScript
*.tsbuildinfo

# Vercel
.vercel

# Backup files
*.bak
*.backup
*~

# Cache directories
.cache
.turbo
.parcel-cache

# Local development
.local
