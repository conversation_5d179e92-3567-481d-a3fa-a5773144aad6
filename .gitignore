# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*.suo
*.ntvs*
*.njsproj
*.sln

# OS generated files
Thumbs.db
.DS_Store
.AppleDouble
.LSOverride

# Video streaming files (HLS segments and playlists)
/public/streams/
*.m3u8
*.ts

# FFmpeg temporary files
ffmpeg_*.log
*.ffmpeg

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
.tmp/

# Backup files
*.bak
*.backup
*~

# Cache directories
.cache/
.turbo/

# Local development files
.local
